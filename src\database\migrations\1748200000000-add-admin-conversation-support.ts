import { MigrationInterface, QueryRunner, TableColumn, Table, Index } from 'typeorm';

export class AddAdminConversationSupport1748200000000 implements MigrationInterface {
  name = 'AddAdminConversationSupport1748200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns to conversation table
    await queryRunner.addColumns('conversation', [
      new TableColumn({
        name: 'is_admin_conversation',
        type: 'boolean',
        default: false,
      }),
      new TableColumn({
        name: 'admin_conversation_user_id',
        type: 'uuid',
        isNullable: true,
      }),
    ]);

    // Add foreign key constraint for admin_conversation_user_id
    await queryRunner.query(`
      ALTER TABLE "conversation" 
      ADD CONSTRAINT "FK_conversation_admin_conversation_user_id" 
      FOREIGN KEY ("admin_conversation_user_id") 
      REFERENCES "user"("id") 
      ON DELETE SET NULL
    `);

    // Add new columns to message table
    await queryRunner.addColumns('message', [
      new TableColumn({
        name: 'actual_sender_id',
        type: 'uuid',
        isNullable: true,
      }),
    ]);

    // Add foreign key constraint for actual_sender_id
    await queryRunner.query(`
      ALTER TABLE "message" 
      ADD CONSTRAINT "FK_message_actual_sender_id" 
      FOREIGN KEY ("actual_sender_id") 
      REFERENCES "user"("id") 
      ON DELETE SET NULL
    `);

    // Create admin_conversation_participant table
    await queryRunner.createTable(
      new Table({
        name: 'admin_conversation_participant',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'now()',
            isNullable: true,
          },
          {
            name: 'created_by',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'conversation_id',
            type: 'uuid',
          },
          {
            name: 'admin_id',
            type: 'uuid',
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'last_accessed_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'unread_count',
            type: 'integer',
            default: 0,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['conversation_id'],
            referencedTableName: 'conversation',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['admin_id'],
            referencedTableName: 'user',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          new Index('IDX_admin_conversation_participant_conversation_admin', ['conversation_id', 'admin_id'], {
            isUnique: true,
          }),
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop admin_conversation_participant table
    await queryRunner.dropTable('admin_conversation_participant');

    // Remove foreign key constraints and columns from message table
    await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_message_actual_sender_id"`);
    await queryRunner.dropColumn('message', 'actual_sender_id');

    // Remove foreign key constraints and columns from conversation table
    await queryRunner.query(`ALTER TABLE "conversation" DROP CONSTRAINT "FK_conversation_admin_conversation_user_id"`);
    await queryRunner.dropColumn('conversation', 'admin_conversation_user_id');
    await queryRunner.dropColumn('conversation', 'is_admin_conversation');
  }
}
