import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User } from '../../database/entities/user.entity';
import { ChatService } from './chat.service';
import { AdminChatService } from './admin-chat.service';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  ConversationFilterDto,
  MessageFilterDto,
  PagedConversationListDto,
  PagedMessageListDto,
  ConversationParticipantDto,
  ContactFilterDto,
} from '../../database/models/chat.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { ApiOkResponseWithEmptyData } from '../../common/decorators/api-empty-response.decorator';

@ApiTags('admin-chat')
@Controller('admin/chat')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
export class AdminChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly adminChatService: AdminChatService,
  ) {}

  @Get('conversations')
  @ApiOperation({ summary: 'Get all admin conversations' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'status', required: false, description: 'Conversation status' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Page size', type: Number })
  @ApiOkResponseWithType(PagedConversationListDto, 'Admin conversations retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getAdminConversations(@GetUser() user: User, @Query() filter: ConversationFilterDto): Promise<ApiResponse<PagedConversationListDto>> {
    const conversations = await this.adminChatService.getAdminConversations(filter);
    return ApiResponse.success(conversations, 'Admin conversations retrieved successfully');
  }

  @Post('conversations/:userId')
  @ApiOperation({ summary: 'Create or get admin conversation with a user' })
  @ApiParam({ name: 'userId', description: 'Target user ID' })
  @ApiOkResponseWithType(ConversationDto, 'Admin conversation created or retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async createOrGetAdminConversation(
    @GetUser() admin: User,
    @Param('userId') userId: string,
  ): Promise<ApiResponse<ConversationDto>> {
    const conversation = await this.adminChatService.getOrCreateAdminConversation(admin.id, userId);

    // Map to ConversationDto format
    const conversationDto: ConversationDto = {
      id: conversation.id,
      type: conversation.type,
      status: conversation.status,
      participant: {
        id: conversation.participant2Id,
        name: conversation.participant2?.name || 'User',
        userId: conversation.participant2?.userId || '',
        email: conversation.participant2?.email || '',
        type: conversation.participant2?.type || '',
        profilePicture: conversation.participant2?.profilePicture || null,
        isOnline: false,
      },
      lastMessageAt: conversation.lastMessageAt,
      lastMessageText: conversation.lastMessageText,
      unreadCount: conversation.participant2UnreadCount || 0,
      createdAt: conversation.createdAt,
      isAdminConversation: conversation.isAdminConversation,
    };

    return ApiResponse.success(conversationDto, 'Admin conversation created or retrieved successfully');
  }

  @Post('conversations/:conversationId/messages')
  @ApiOperation({ summary: 'Send message in admin conversation' })
  @ApiParam({ name: 'conversationId', description: 'Conversation ID' })
  @ApiBody({ type: CreateMessageDto })
  @ApiOkResponseWithType(MessageDto, 'Admin message sent successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(404, 'Conversation not found')
  @ApiErrorResponse(500, 'Internal server error')
  async sendAdminMessage(
    @GetUser() admin: User,
    @Param('conversationId') conversationId: string,
    @Body() createMessageDto: CreateMessageDto,
  ): Promise<ApiResponse<MessageDto>> {
    const message = await this.adminChatService.sendAdminMessage(admin.id, conversationId, createMessageDto);
    return ApiResponse.success(message, 'Admin message sent successfully');
  }

  @Get('conversations/user/:userId')
  @ApiOperation({ summary: 'Get admin conversation info by user ID' })
  @ApiParam({ name: 'userId', description: 'Target user ID' })
  @ApiOkResponseWithType(Object, 'Admin conversation info retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getAdminConversationByUserId(
    @GetUser() admin: User,
    @Param('userId') userId: string,
  ): Promise<ApiResponse<{
    conversationId: string;
    userId: string;
    userName: string;
    userProfilePicture: string;
    isNewConversation: boolean;
  }>> {
    const result = await this.adminChatService.getAdminConversationByUserId(admin.id, userId);
    return ApiResponse.success(result, 'Admin conversation info retrieved successfully');
  }

  @Get('users/:userId/conversations')
  @ApiOperation({ summary: 'Get conversations for a specific user (admin)' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'status', required: false, description: 'Conversation status' })
  @ApiQuery({ name: 'type', required: false, description: 'Conversation type' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Page size', type: Number })
  @ApiOkResponseWithType(PagedConversationListDto, 'Conversations retrieved successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getUserConversations(@Param('userId') userId: string, @Query() filter: ConversationFilterDto): Promise<ApiResponse<PagedConversationListDto>> {
    const conversations = await this.chatService.getConversations(userId, filter);
    return ApiResponse.success(conversations, 'Conversations retrieved successfully');
  }

  @Get('conversations/:id')
  @ApiOperation({ summary: 'Get a conversation by ID (admin)' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  @ApiOkResponseWithType(ConversationDto, 'Conversation retrieved successfully')
  @ApiErrorResponse(404, 'Conversation not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getConversation(@GetUser() user: User, @Param('id') id: string): Promise<ApiResponse<ConversationDto>> {
    const conversation = await this.chatService.getConversation(id, user.id);
    return ApiResponse.success(conversation, 'Conversation retrieved successfully');
  }

  @Get('conversations/:id/messages')
  @ApiOperation({ summary: 'Get messages for a conversation (admin)' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  @ApiQuery({ name: 'type', required: false, description: 'Message type' })
  @ApiQuery({ name: 'status', required: false, description: 'Message status' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for partial match on message content' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Page size', type: Number })
  @ApiOkResponseWithType(PagedMessageListDto, 'Messages retrieved successfully')
  @ApiErrorResponse(404, 'Conversation not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getMessages(@GetUser() user: User, @Param('id') id: string, @Query() filter: MessageFilterDto): Promise<ApiResponse<PagedMessageListDto>> {
    const messages = await this.chatService.getMessages(id, user.id, filter);
    return ApiResponse.success(messages, 'Messages retrieved successfully');
  }

  @Post('messages')
  @ApiOperation({ summary: 'Send a message (admin)' })
  @ApiBody({ type: CreateMessageDto })
  @ApiOkResponseWithType(MessageDto, 'Message sent successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(500, 'Internal server error')
  async sendMessage(@GetUser() user: User, @Body() createMessageDto: CreateMessageDto): Promise<ApiResponse<MessageDto>> {
    const message = await this.chatService.sendMessage(user.id, createMessageDto);
    return ApiResponse.success(message, 'Message sent successfully');
  }

  @Get('users')
  @ApiOperation({ summary: 'Get all users who can chat (admin)' })
  @ApiQuery({ name: 'name', required: false, description: 'Filter by name' })
  @ApiQuery({ name: 'email', required: false, description: 'Filter by email' })
  @ApiQuery({ name: 'phone', required: false, description: 'Filter by phone' })
  @ApiOkResponseWithArrayType(ConversationParticipantDto, 'Users retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getChatUsers(@GetUser() user: User, @Query() filter: ContactFilterDto): Promise<ApiResponse<ConversationParticipantDto[]>> {
    const contacts = await this.chatService.getChatContacts(user.id, filter);
    return ApiResponse.success(contacts, 'Users retrieved successfully');
  }

  @Post('messages/:id/read')
  @ApiOperation({ summary: 'Mark messages as read (admin)' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  @ApiOkResponseWithEmptyData('Messages marked as read')
  @ApiErrorResponse(404, 'Conversation not found')
  @ApiErrorResponse(500, 'Internal server error')
  async markMessagesAsRead(@GetUser() user: User, @Param('id') id: string): Promise<ApiResponse<null>> {
    await this.chatService.markMessagesAsRead(id, user.id);
    return ApiResponse.success(null, 'Messages marked as read');
  }
}
