import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { AsyncNotificationHelperService } from './async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';

@Processor('notifications')
export class NotificationProcessor {
  private readonly logger = new Logger(NotificationProcessor.name);

  constructor(private readonly asyncNotificationHelper: AsyncNotificationHelperService) {}

  @Process('send-notification')
  async handleNotification(job: Job<{
    userId: string;
    type: NotificationType;
    title: string;
    message: string;
    options?: any;
    metadata?: any;
  }>): Promise<void> {
    this.logger.debug(`Processing notification job ${job.id}`);
    try {
      await this.asyncNotificationHelper.notifyAsync(
        job.data.userId,
        job.data.type,
        job.data.title,
        job.data.message,
        job.data.options || {},
        job.data.metadata || {}
      );
    } catch (error) {
      this.logger.error(`Failed to process notification job ${job.id}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
