import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Conversation, ConversationStatus, ConversationType } from '../../database/entities/conversation.entity';
import { Message, MessageStatus, MessageType } from '../../database/entities/message.entity';
import { AdminConversationParticipant } from '../../database/entities/admin-conversation-participant.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  ConversationFilterDto,
  ConversationParticipantDto,
  PagedConversationListDto,
} from '../../database/models/chat.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';

/**
 * Service for handling admin chat functionality
 * Manages shared admin conversations where any admin can participate
 */
@Injectable()
export class AdminChatService {
  private readonly logger = new Logger(AdminChatService.name);

  constructor(
    @InjectRepository(Conversation)
    private readonly conversationRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(AdminConversationParticipant)
    private readonly adminConversationParticipantRepository: Repository<AdminConversationParticipant>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly notificationHelper: NotificationHelperService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Get or create a shared admin conversation with a user
   * @param adminId The admin initiating the conversation
   * @param targetUserId The user to chat with (tutor or student)
   * @returns The shared admin conversation
   */
  async getOrCreateAdminConversation(adminId: string, targetUserId: string): Promise<Conversation> {
    try {
      // Verify admin user
      const admin = await this.userRepository.findOne({ where: { id: adminId } });
      if (!admin || admin.type !== UserType.ADMIN) {
        throw new BadRequestException('Only admins can create admin conversations');
      }

      // Verify target user
      const targetUser = await this.userRepository.findOne({ where: { id: targetUserId } });
      if (!targetUser) {
        throw new NotFoundException('Target user not found');
      }

      if (targetUser.type === UserType.ADMIN) {
        throw new BadRequestException('Cannot create admin conversation with another admin');
      }

      // Check if admin conversation already exists for this user
      let conversation = await this.conversationRepository.findOne({
        where: {
          isAdminConversation: true,
          adminConversationUserId: targetUserId,
        },
        relations: ['adminConversationUser', 'participant2'],
      });

      if (conversation) {
        // Ensure the requesting admin is added as a participant
        await this.ensureAdminParticipant(conversation.id, adminId);
        return conversation;
      }

      // Create new admin conversation using transaction
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Create the conversation with a virtual admin as participant1
        // We'll use the first admin as the virtual admin participant
        conversation = queryRunner.manager.create(Conversation, {
          participant1Id: adminId, // This represents the "admin" side
          participant2Id: targetUserId,
          type: ConversationType.DIRECT,
          status: ConversationStatus.ACTIVE,
          isAdminConversation: true,
          adminConversationUserId: targetUserId,
        });

        conversation = await queryRunner.manager.save(conversation);

        // Add the requesting admin as a participant
        const adminParticipant = queryRunner.manager.create(AdminConversationParticipant, {
          conversationId: conversation.id,
          adminId: adminId,
          isActive: true,
          lastAccessedAt: getCurrentUTCDate(),
        });

        await queryRunner.manager.save(adminParticipant);

        await queryRunner.commitTransaction();

        this.logger.log(`Created admin conversation ${conversation.id} between admin ${adminId} and user ${targetUserId}`);
        return conversation;
      } catch (error) {
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(`Error getting or creating admin conversation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all admin conversations
   * @param filter Filter options
   * @returns Paged list of admin conversations
   */
  async getAdminConversations(filter: ConversationFilterDto): Promise<PagedConversationListDto> {
    try {
      const page = filter.page || 1;
      const limit = filter.limit || 10;
      const skip = (page - 1) * limit;

      const queryBuilder = this.conversationRepository
        .createQueryBuilder('conversation')
        .leftJoinAndSelect('conversation.participant1', 'participant1')
        .leftJoinAndSelect('conversation.participant2', 'participant2')
        .leftJoinAndSelect('conversation.adminConversationUser', 'adminConversationUser')
        .where('conversation.isAdminConversation = :isAdmin', { isAdmin: true })
        .orderBy('conversation.lastMessageAt', 'DESC')
        .addOrderBy('conversation.createdAt', 'DESC');

      // Apply filters
      if (filter.status) {
        queryBuilder.andWhere('conversation.status = :status', { status: filter.status });
      }

      if (filter.search) {
        queryBuilder.andWhere(
          '(participant2.name ILIKE :search OR participant2.userId ILIKE :search OR adminConversationUser.name ILIKE :search OR adminConversationUser.userId ILIKE :search)',
          { search: `%${filter.search}%` },
        );
      }

      // Get total count
      const total = await queryBuilder.getCount();

      // Get conversations
      const conversations = await queryBuilder.skip(skip).take(limit).getMany();

      // Map to DTOs
      const conversationDtos = await Promise.all(
        conversations.map(async (conversation) => {
          return this.mapAdminConversationToDto(conversation);
        }),
      );

      return {
        items: conversationDtos,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error getting admin conversations: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a conversation is an admin conversation
   * @param conversationId The conversation ID
   * @returns True if it's an admin conversation
   */
  async isAdminConversation(conversationId: string): Promise<boolean> {
    const conversation = await this.conversationRepository.findOne({
      where: { id: conversationId },
    });
    return conversation?.isAdminConversation === true;
  }

  /**
   * Send a message in an admin conversation
   * @param adminId The admin sending the message
   * @param conversationId The conversation ID
   * @param createMessageDto Message data
   * @returns The created message
   */
  async sendAdminMessage(adminId: string, conversationId: string, createMessageDto: CreateMessageDto): Promise<MessageDto> {
    try {
      // Verify admin user
      const admin = await this.userRepository.findOne({ where: { id: adminId } });
      if (!admin || admin.type !== UserType.ADMIN) {
        throw new BadRequestException('Only admins can send admin messages');
      }

      // Get the admin conversation
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId, isAdminConversation: true },
        relations: ['participant1', 'participant2', 'adminConversationUser'],
      });

      if (!conversation) {
        throw new NotFoundException('Admin conversation not found');
      }

      // Verify admin is a participant
      const adminParticipant = await this.adminConversationParticipantRepository.findOne({
        where: { conversationId, adminId, isActive: true },
      });

      if (!adminParticipant) {
        // Add admin as participant if not already
        await this.ensureAdminParticipant(conversationId, adminId);
      }

      // Create message
      const message = this.messageRepository.create({
        conversationId,
        senderId: conversation.participant1Id, // Virtual admin participant
        recipientId: conversation.participant2Id,
        actualSenderId: adminId, // Track the actual admin who sent the message
        type: createMessageDto.type || MessageType.TEXT,
        content: createMessageDto.content,
        metadata: createMessageDto.metadata,
        status: MessageStatus.SENT,
      });

      const savedMessage = await this.messageRepository.save(message);

      // Update conversation last message info
      await this.conversationRepository.update(conversationId, {
        lastMessageAt: savedMessage.createdAt,
        lastMessageText: savedMessage.content,
        lastMessageSenderId: savedMessage.senderId,
        participant2UnreadCount: conversation.participant2UnreadCount + 1,
      });

      // Update admin participant access time
      await this.adminConversationParticipantRepository.update(
        { conversationId, adminId },
        { lastAccessedAt: getCurrentUTCDate() },
      );

      // Send notification to the target user
      await this.notificationHelper.notify(
        conversation.participant2Id,
        NotificationType.CHAT_MESSAGE,
        'New message from Admin',
        `You have a new message from admin`,
        {
          relatedEntityId: savedMessage.id,
          relatedEntityType: 'chat_message',
          sendInApp: true,
          sendPush: true,
          sendRealtime: true,
        },
      );

      // Map to DTO
      return this.mapMessageToDto(savedMessage, admin);
    } catch (error) {
      this.logger.error(`Error sending admin message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Ensure an admin is added as a participant in an admin conversation
   * @param conversationId The conversation ID
   * @param adminId The admin ID
   */
  private async ensureAdminParticipant(conversationId: string, adminId: string): Promise<void> {
    const existingParticipant = await this.adminConversationParticipantRepository.findOne({
      where: { conversationId, adminId },
    });

    if (!existingParticipant) {
      const adminParticipant = this.adminConversationParticipantRepository.create({
        conversationId,
        adminId,
        isActive: true,
        lastAccessedAt: getCurrentUTCDate(),
      });

      await this.adminConversationParticipantRepository.save(adminParticipant);
      this.logger.log(`Added admin ${adminId} as participant in conversation ${conversationId}`);
    } else if (!existingParticipant.isActive) {
      // Reactivate if inactive
      await this.adminConversationParticipantRepository.update(
        { conversationId, adminId },
        { isActive: true, lastAccessedAt: getCurrentUTCDate() },
      );
      this.logger.log(`Reactivated admin ${adminId} in conversation ${conversationId}`);
    }
  }

  /**
   * Map admin conversation entity to DTO
   * @param conversation Conversation entity
   * @returns Conversation DTO
   */
  private mapAdminConversationToDto(conversation: Conversation): ConversationDto {
    const targetUser = conversation.adminConversationUser || conversation.participant2;

    return {
      id: conversation.id,
      type: conversation.type,
      status: conversation.status,
      participant: this.mapToParticipantDto(targetUser),
      lastMessageAt: conversation.lastMessageAt,
      lastMessageText: conversation.lastMessageText,
      unreadCount: conversation.participant2UnreadCount || 0,
      createdAt: conversation.createdAt,
      isAdminConversation: true,
    };
  }

  /**
   * Map message entity to DTO
   * @param message Message entity
   * @param actualSender The actual admin who sent the message
   * @returns Message DTO
   */
  private mapMessageToDto(message: Message, actualSender?: User): MessageDto {
    return {
      id: message.id,
      conversationId: message.conversationId,
      senderId: message.senderId,
      senderName: actualSender ? actualSender.name : 'Admin',
      recipientId: message.recipientId,
      type: message.type,
      content: message.content,
      status: message.status,
      readAt: message.readAt,
      deliveredAt: message.deliveredAt,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      metadata: message.metadata,
      // For admin messages, include actual sender info
      actualSender: actualSender ? {
        id: actualSender.id,
        name: actualSender.name,
        userId: actualSender.userId,
        email: actualSender.email,
        type: actualSender.type,
      } : undefined,
      attachments: [], // TODO: Handle attachments if needed
    };
  }

  /**
   * Map user entity to participant DTO
   * @param user User entity
   * @returns Participant DTO
   */
  private mapToParticipantDto(user: User): ConversationParticipantDto {
    return {
      id: user.id,
      name: user.name,
      userId: user.userId,
      email: user.email,
      type: user.type,
      profilePicture: null, // TODO: Get profile picture URL
      isOnline: false, // TODO: Check online status from gateway
    };
  }
}
