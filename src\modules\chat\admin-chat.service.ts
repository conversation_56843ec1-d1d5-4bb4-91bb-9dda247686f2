import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Conversation, ConversationStatus, ConversationType } from '../../database/entities/conversation.entity';
import { Message, MessageStatus, MessageType } from '../../database/entities/message.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { AsyncNotificationHelper } from '../notification/async-notification.helper';
import { NotificationType } from '../../database/entities/notification.entity';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  ConversationFilterDto,
  ConversationParticipantDto,
  PagedConversationListDto,
} from '../../database/models/chat.dto';le, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Conversation, ConversationStatus, ConversationType } from '../../database/entities/conversation.entity';
import { Message, MessageStatus, MessageType } from '../../database/entities/message.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  ConversationFilterDto,
  ConversationParticipantDto,
  PagedConversationListDto,
} from '../../database/models/chat.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { NotificationType } from 'src/database/entities/notification.entity';

@Injectable()
export class AdminChatService {
  private readonly logger = new Logger(AdminChatService.name);

  constructor(
    @InjectRepository(Conversation)
    private readonly conversationRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly asyncNotificationHelper: AsyncNotificationHelper,
  ) {}

  /**
   * Create or get an admin shared conversation with a user
   * @param userId The ID of the user to create/get conversation with
   * @param adminId The ID of the admin creating the conversation
   */
  async createOrGetAdminConversation(userId: string, adminId: string): Promise<ConversationDto> {
    // Check if user exists and is not an admin
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.type === UserType.ADMIN) {
      throw new BadRequestException('Cannot create admin conversation with another admin');
    }

    // Check for existing admin conversation
    let conversation = await this.conversationRepository.findOne({
      where: {
        type: ConversationType.ADMIN_SHARED,
        participant2Id: userId,
        status: ConversationStatus.ACTIVE,
      },
      relations: ['participant1', 'participant2'],
    });

    if (!conversation) {
      // Create new admin conversation
      conversation = this.conversationRepository.create({
        type: ConversationType.ADMIN_SHARED,
        participant1Id: adminId, // Initial admin creator
        participant2Id: userId, // The user
        status: ConversationStatus.ACTIVE,
      });
      await this.conversationRepository.save(conversation);
    }

    return this.mapToConversationDto(conversation);
  }

  /**
   * Get all admin shared conversations
   */
  async getAdminSharedConversations(filter: ConversationFilterDto): Promise<PagedConversationListDto> {
    const query = this.conversationRepository
      .createQueryBuilder('conversation')
      .where('conversation.type = :type', { type: ConversationType.ADMIN_SHARED })
      .leftJoinAndSelect('conversation.participant1', 'participant1')
      .leftJoinAndSelect('conversation.participant2', 'participant2');

    if (filter.status) {
      query.andWhere('conversation.status = :status', { status: filter.status });
    }

    const [conversations, total] = await query
      .skip((filter.page - 1) * filter.limit)
      .take(filter.limit)
      .orderBy(`conversation.${filter.sortBy}`, filter.sortOrder)
      .getManyAndCount();

    return {
      items: conversations.map(conv => this.mapToConversationDto(conv)),
      total,
      page: filter.page,
      limit: filter.limit,
    };
  }

  /**
   * Check if a conversation is an admin shared conversation
   */
  async isAdminSharedConversation(conversationId: string): Promise<boolean> {
    const conversation = await this.conversationRepository.findOne({
      where: { id: conversationId },
    });
    return conversation?.type === ConversationType.ADMIN_SHARED;
  }

  /**
   * Send message in admin shared conversation
   */
  async sendAdminMessage(adminId: string, createMessageDto: CreateMessageDto): Promise<MessageDto> {
    const conversation = await this.conversationRepository.findOne({
      where: { 
        id: createMessageDto.conversationId,
        type: ConversationType.ADMIN_SHARED 
      },
    });

    if (!conversation) {
      throw new NotFoundException('Admin shared conversation not found');
    }

    const message = this.messageRepository.create({
      conversationId: conversation.id,
      senderId: adminId,
      recipientId: conversation.participant2Id,
      content: createMessageDto.content,
      type: createMessageDto.type,
      status: MessageStatus.SENT,
    });

    await this.messageRepository.save(message);

      // Send notification to the user
      await this.notificationHelper.notify(
        conversation.participant2Id,
        NotificationType.CHAT_MESSAGE,
        'New message from Admin Support',
        'You have a new message from admin support',
        {
          relatedEntityId: message.id,
          relatedEntityType: 'chat_message',
          sendRealtime: true,
          sendInApp: true,
          deepLink: `/chat/conversations/${conversation.id}`
        }
      );    return this.mapToMessageDto(message);
  }

  private mapToConversationDto(conversation: Conversation): ConversationDto {
    return {
      id: conversation.id,
      type: conversation.type,
      status: conversation.status,
      participant: this.mapToParticipantDto(conversation.participant2), // Map user to participant
      lastMessageAt: conversation.lastMessageAt,
      lastMessageText: conversation.lastMessageText,
      unreadCount: 0, // This should be calculated based on unread messages
      createdAt: conversation.createdAt,
    };
  }

  private mapToMessageDto(message: Message): MessageDto {
    return {
      id: message.id,
      conversationId: message.conversationId,
      senderId: message.senderId,
      senderName: 'Admin Support',
      recipientId: message.recipientId,
      content: message.content,
      type: message.type,
      status: message.status,
      createdAt: message.createdAt,
    };
  }

  private mapToParticipantDto(user: User): ConversationParticipantDto {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      type: user.type,
      profilePicture: user.profilePicture,
      isOnline: false, // This should be checked from the chat gateway
    };
  }
}
