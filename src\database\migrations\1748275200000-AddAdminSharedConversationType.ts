import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdminSharedConversationType1748275200000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // First backup existing enum values
        await queryRunner.query(`
            CREATE TYPE "conversation_type_enum_new" AS ENUM ('direct', 'group', 'admin_shared');
        `);

        // Update the column to use the new enum type
        await queryRunner.query(`
            ALTER TABLE "conversation" 
            ALTER COLUMN "type" TYPE "conversation_type_enum_new" 
            USING ("type"::text::"conversation_type_enum_new");
        `);

        // Drop the old enum type
        await queryRunner.query(`
            DROP TYPE "conversation_type_enum";
        `);

        // Rename the new enum type to the original name
        await queryRunner.query(`
            ALTER TYPE "conversation_type_enum_new" 
            RENAME TO "conversation_type_enum";
        `);

        // Add indexes for efficient querying
        await queryRunner.query(`
            CREATE INDEX "idx_conversation_type" ON "conversation" ("type");
        `);

        await queryRunner.query(`
            CREATE INDEX "idx_admin_shared_conversations" 
            ON "conversation" ("type", "participant2_id") 
            WHERE type = 'admin_shared';
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the indexes
        await queryRunner.query(`
            DROP INDEX IF EXISTS "idx_conversation_type";
        `);

        await queryRunner.query(`
            DROP INDEX IF EXISTS "idx_admin_shared_conversations";
        `);

        // Create new enum without admin_shared
        await queryRunner.query(`
            CREATE TYPE "conversation_type_enum_new" AS ENUM ('direct', 'group');
        `);

        // Update any admin_shared conversations to be direct
        await queryRunner.query(`
            UPDATE "conversation" 
            SET "type" = 'direct' 
            WHERE "type" = 'admin_shared';
        `);

        // Update the column to use the new enum type
        await queryRunner.query(`
            ALTER TABLE "conversation" 
            ALTER COLUMN "type" TYPE "conversation_type_enum_new" 
            USING ("type"::text::"conversation_type_enum_new");
        `);

        // Drop the old enum type
        await queryRunner.query(`
            DROP TYPE "conversation_type_enum";
        `);

        // Rename the new enum type to the original name
        await queryRunner.query(`
            ALTER TYPE "conversation_type_enum_new" 
            RENAME TO "conversation_type_enum";
        `);
    }
}
