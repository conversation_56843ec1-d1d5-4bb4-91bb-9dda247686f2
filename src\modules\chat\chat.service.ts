import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Not, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Equal, getRepository } from 'typeorm';
import { Conversation, ConversationStatus, ConversationType } from '../../database/entities/conversation.entity';
import { Message, MessageStatus, MessageType } from '../../database/entities/message.entity';
import { MessageAttachment } from '../../database/entities/message-attachment.entity';
import { MessageRegistry } from '../../database/entities/message-registry.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { StudentFriendship, FriendshipStatus } from '../../database/entities/student-friendship.entity';
import { AdminConversationParticipant } from '../../database/entities/admin-conversation-participant.entity';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  UpdateMessageDto,
  ConversationFilterDto,
  MessageFilterDto,
  PagedConversationListDto,
  PagedMessageListDto,
  ConversationParticipantDto,
  MessageAttachmentDto,
  ChatFileUploadResponseDto,
  ContactFilterDto,
} from '../../database/models/chat.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { MulterFile } from '../../common/interfaces/multer-file.interface';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);
  private readonly uploadDir: string;

  constructor(
    @InjectRepository(Conversation)
    private readonly conversationRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(MessageAttachment)
    private readonly messageAttachmentRepository: Repository<MessageAttachment>,
    @InjectRepository(MessageRegistry)
    private readonly messageRegistryRepository: Repository<MessageRegistry>,
    @InjectRepository(AdminConversationParticipant)
    private readonly adminConversationParticipantRepository: Repository<AdminConversationParticipant>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(StudentFriendship)
    private readonly studentFriendshipRepository: Repository<StudentFriendship>,
    private readonly notificationHelper: NotificationHelperService,
    private readonly fileRegistryService: FileRegistryService,
    private readonly deeplinkService: DeeplinkService,
    private readonly configService: ConfigService,
  ) {
    this.uploadDir = this.configService.get<string>('UPLOAD_DIR') || 'uploads';
    // Ensure the chat uploads directory exists
    const chatUploadsDir = path.join(this.uploadDir, 'chat');
    if (!fs.existsSync(chatUploadsDir)) {
      fs.mkdirSync(chatUploadsDir, { recursive: true });
    }
  }

  /**
   * Check if two users can chat with each other
   * @param userId1 First user ID
   * @param userId2 Second user ID
   * @returns True if users can chat, false otherwise
   */
  async canUsersChat(userId1: string, userId2: string): Promise<boolean> {
    try {
      // Get user types
      const [user1, user2] = await Promise.all([this.userRepository.findOne({ where: { id: userId1 } }), this.userRepository.findOne({ where: { id: userId2 } })]);

      if (!user1 || !user2) {
        return false;
      }

      // Admin can chat with anyone
      if (user1.type === UserType.ADMIN || user2.type === UserType.ADMIN) {
        return true;
      }

      // Student-Tutor: Check if they are mapped
      if ((user1.type === UserType.STUDENT && user2.type === UserType.TUTOR) || (user1.type === UserType.TUTOR && user2.type === UserType.STUDENT)) {
        const studentId = user1.type === UserType.STUDENT ? user1.id : user2.id;
        const tutorId = user1.type === UserType.TUTOR ? user1.id : user2.id;

        // Check if there's any active mapping between the student and tutor
        // regardless of the plan feature
        this.logger.log(`Checking mapping for student ${studentId} and tutor ${tutorId}`);

        // First, check for active mappings
        const activeMapping = await this.studentTutorMappingRepository.findOne({
          where: {
            studentId,
            tutorId,
            status: MappingStatus.ACTIVE,
          },
        });

        if (activeMapping) {
          this.logger.log(`Found active mapping for student ${studentId} and tutor ${tutorId} with plan feature ${activeMapping.planFeatureId}`);
          return true;
        }

        // If no active mapping, check for any mapping
        this.logger.warn(`No active mapping found for student ${studentId} and tutor ${tutorId}`);

        // Get all mappings for this student-tutor pair
        const allMappings = await this.studentTutorMappingRepository.find({
          where: {
            studentId,
            tutorId,
          },
        });

        if (allMappings.length > 0) {
          this.logger.warn(`Found ${allMappings.length} inactive mappings for student ${studentId} and tutor ${tutorId}`);
          allMappings.forEach((m) => {
            this.logger.warn(`Mapping: id=${m.id}, status=${m.status}, planFeatureId=${m.planFeatureId}`);
          });

          // Allow chat if there's any mapping, even if inactive
          return true;
        } else {
          this.logger.warn(`No mapping at all found for student ${studentId} and tutor ${tutorId}`);
          return false;
        }
      }

      // Student-Student: Check if they are friends
      if (user1.type === UserType.STUDENT && user2.type === UserType.STUDENT) {
        // Check if there's an accepted friendship between the students
        const friendship = await this.studentFriendshipRepository.findOne({
          where: [
            { requesterId: userId1, requestedId: userId2, status: FriendshipStatus.ACCEPTED },
            { requesterId: userId2, requestedId: userId1, status: FriendshipStatus.ACCEPTED },
          ],
        });

        return !!friendship;
      }

      // For now, no tutor-tutor chat
      if (user1.type === UserType.TUTOR && user2.type === UserType.TUTOR) {
        return false;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error checking if users can chat: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Get or create a conversation between two users
   * @param userId1 First user ID
   * @param userId2 Second user ID
   * @returns Conversation entity
   */
  async getOrCreateConversation(userId1: string, userId2: string): Promise<Conversation> {
    try {
      // Check if users can chat
      const canChat = await this.canUsersChat(userId1, userId2);
      if (!canChat) {
        this.logger.error(`Users ${userId1} and ${userId2} cannot chat with each other`);

        // Get user types for better error message
        const [user1, user2] = await Promise.all([this.userRepository.findOne({ where: { id: userId1 } }), this.userRepository.findOne({ where: { id: userId2 } })]);

        if (user1 && user2) {
          this.logger.error(`User types: ${user1.type} (${userId1}) and ${user2.type} (${userId2})`);

          // If it's a student-tutor pair, provide more specific error
          if ((user1.type === UserType.STUDENT && user2.type === UserType.TUTOR) || (user1.type === UserType.TUTOR && user2.type === UserType.STUDENT)) {
            const studentId = user1.type === UserType.STUDENT ? user1.id : user2.id;
            const tutorId = user1.type === UserType.TUTOR ? user1.id : user2.id;

            throw new ForbiddenException(`Student ${studentId} and tutor ${tutorId} are not mapped to each other`);
          }
        }

        throw new ForbiddenException('Users cannot chat with each other');
      }

      // Check if conversation already exists
      // For admin conversations, we need to check for shared admin conversations
      const [user1, user2] = await Promise.all([
        this.userRepository.findOne({ where: { id: userId1 } }),
        this.userRepository.findOne({ where: { id: userId2 } })
      ]);

      // If one user is admin, check for existing admin conversation
      if (user1?.type === UserType.ADMIN || user2?.type === UserType.ADMIN) {
        const adminId = user1?.type === UserType.ADMIN ? userId1 : userId2;
        const targetUserId = user1?.type === UserType.ADMIN ? userId2 : userId1;

        // Check for existing admin conversation
        let conversation = await this.conversationRepository.findOne({
          where: {
            isAdminConversation: true,
            adminConversationUserId: targetUserId,
          },
        });

        if (conversation) {
          // Ensure the requesting admin is added as a participant
          await this.ensureAdminParticipant(conversation.id, adminId);
          return conversation;
        }
      }

      // For regular conversations, check normal way
      let conversation = await this.conversationRepository.findOne({
        where: [
          { participant1Id: userId1, participant2Id: userId2, isAdminConversation: false },
          { participant1Id: userId2, participant2Id: userId1, isAdminConversation: false },
        ],
      });

      // If conversation exists, return it
      if (conversation) {
        // If conversation is archived or blocked, reactivate it
        if (conversation.status !== ConversationStatus.ACTIVE) {
          conversation.status = ConversationStatus.ACTIVE;
          conversation = await this.conversationRepository.save(conversation);
        }
        return conversation;
      }

      // Create new conversation
      // If one user is admin, create admin conversation
      if (user1?.type === UserType.ADMIN || user2?.type === UserType.ADMIN) {
        const adminId = user1?.type === UserType.ADMIN ? userId1 : userId2;
        const targetUserId = user1?.type === UserType.ADMIN ? userId2 : userId1;

        const newConversation = this.conversationRepository.create({
          participant1Id: adminId, // Admin side
          participant2Id: targetUserId,
          type: ConversationType.DIRECT,
          status: ConversationStatus.ACTIVE,
          isAdminConversation: true,
          adminConversationUserId: targetUserId,
        });

        const savedConversation = await this.conversationRepository.save(newConversation);

        // Add admin as participant
        await this.ensureAdminParticipant(savedConversation.id, adminId);

        return savedConversation;
      }

      // Create regular conversation
      const newConversation = this.conversationRepository.create({
        participant1Id: userId1,
        participant2Id: userId2,
        type: ConversationType.DIRECT,
        status: ConversationStatus.ACTIVE,
        isAdminConversation: false,
      });

      return await this.conversationRepository.save(newConversation);
    } catch (error) {
      this.logger.error(`Error getting or creating conversation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get conversations for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns Paged list of conversations
   */
  async getConversations(userId: string, filter: ConversationFilterDto): Promise<PagedConversationListDto> {
    try {
      const page = filter.page || 1;
      const limit = filter.limit || 10;
      const skip = (page - 1) * limit;

      // Build query
      const queryBuilder = this.conversationRepository
        .createQueryBuilder('conversation')
        .leftJoinAndSelect('conversation.participant1', 'participant1')
        .leftJoinAndSelect('conversation.participant2', 'participant2')
        .where('(conversation.participant1Id = :userId OR conversation.participant2Id = :userId)', { userId })
        .orderBy('conversation.lastMessageAt', 'DESC')
        .addOrderBy('conversation.createdAt', 'DESC');

      // Apply filters
      if (filter.status) {
        queryBuilder.andWhere('conversation.status = :status', { status: filter.status });
      }

      if (filter.type) {
        queryBuilder.andWhere('conversation.type = :type', { type: filter.type });
      }

      if (filter.search) {
        queryBuilder.andWhere('(participant1.name ILIKE :search OR participant2.name ILIKE :search OR participant1.userId ILIKE :search OR participant2.userId ILIKE :search)', {
          search: `%${filter.search}%`,
        });
      }

      // Get total count
      const total = await queryBuilder.getCount();

      // Get conversations
      const conversations = await queryBuilder.skip(skip).take(limit).getMany();

      // Map to DTOs
      const conversationDtos = await Promise.all(
        conversations.map(async (conversation) => {
          return this.mapConversationToDto(conversation, userId);
        }),
      );

      return {
        items: conversationDtos,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error getting conversations: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a conversation by ID
   * @param conversationId Conversation ID
   * @param userId User ID (for authorization)
   * @returns Conversation DTO
   */
  async getConversation(conversationId: string, userId: string): Promise<ConversationDto> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
        relations: ['participant1', 'participant2'],
      });

      if (!conversation) {
        throw new NotFoundException('Conversation not found');
      }

      // Check if user is a participant
      if (conversation.participant1Id !== userId && conversation.participant2Id !== userId) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      // If there's no last message in the conversation entity, try to fetch the latest message
      if (!conversation.lastMessageText && conversation.id) {
        try {
          const latestMessage = await this.messageRepository.findOne({
            where: { conversationId: conversation.id },
            order: { createdAt: 'DESC' },
          });

          if (latestMessage) {
            conversation.lastMessageText = latestMessage.content;
            conversation.lastMessageAt = latestMessage.createdAt;
            conversation.lastMessageSenderId = latestMessage.senderId;
          }
        } catch (error) {
          this.logger.error(`Error fetching latest message for conversation ${conversation.id}: ${error.message}`, error.stack);
        }
      }

      return this.mapConversationToDto(conversation, userId);
    } catch (error) {
      this.logger.error(`Error getting conversation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get messages for a conversation
   * @param conversationId Conversation ID
   * @param userId User ID (for authorization)
   * @param filter Filter options
   * @returns Paged list of messages
   */
  async getMessages(conversationId: string, userId: string, filter: MessageFilterDto): Promise<PagedMessageListDto> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      if (!conversation) {
        // Return empty result set instead of throwing an error
        this.logger.warn(`Conversation ${conversationId} not found, returning empty result set`);
        return {
          items: [],
          total: 0,
          page: filter.page || 1,
          limit: filter.limit || 20,
        };
      }

      // Check if user is a participant
      if (conversation.participant1Id !== userId && conversation.participant2Id !== userId) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      const page = filter.page || 1;
      const limit = filter.limit || 20;
      const skip = (page - 1) * limit;

      // Build query
      const queryBuilder = this.messageRepository
        .createQueryBuilder('message')
        .leftJoinAndSelect('message.sender', 'sender')
        .leftJoinAndSelect('message.attachments', 'attachments')
        .where('message.conversationId = :conversationId', { conversationId })
        .orderBy('message.createdAt', 'DESC');

      // Apply filters
      if (filter.type) {
        queryBuilder.andWhere('message.type = :type', { type: filter.type });
      }

      if (filter.status) {
        queryBuilder.andWhere('message.status = :status', { status: filter.status });
      }

      if (filter.search) {
        queryBuilder.andWhere('message.content ILIKE :search', { search: `%${filter.search}%` });
      }

      // Get total count
      const total = await queryBuilder.getCount();

      // Get messages
      const messages = await queryBuilder.skip(skip).take(limit).getMany();

      // Mark messages as read
      await this.markMessagesAsRead(conversationId, userId);

      // Map to DTOs
      const messageDtos = await Promise.all(messages.map((message) => this.mapMessageToDto(message)));

      return {
        items: messageDtos,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error getting messages: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a message
   * @param senderId Sender ID
   * @param createMessageDto Message data
   * @returns Created message
   */
  async sendMessage(senderId: string, createMessageDto: CreateMessageDto): Promise<MessageDto> {
    try {
      const { recipientId, type = MessageType.TEXT, content, metadata, attachmentIds } = createMessageDto;

      // Check if users can chat
      const canChat = await this.canUsersChat(senderId, recipientId);
      if (!canChat) {
        this.logger.error(`Users ${senderId} and ${recipientId} cannot chat with each other`);

        // Get user types for better error message
        const [sender, recipient] = await Promise.all([this.userRepository.findOne({ where: { id: senderId } }), this.userRepository.findOne({ where: { id: recipientId } })]);

        if (sender && recipient) {
          this.logger.error(`User types: ${sender.type} (${senderId}) and ${recipient.type} (${recipientId})`);

          // If it's a student-tutor pair, provide more specific error
          if ((sender.type === UserType.STUDENT && recipient.type === UserType.TUTOR) || (sender.type === UserType.TUTOR && recipient.type === UserType.STUDENT)) {
            const studentId = sender.type === UserType.STUDENT ? sender.id : recipient.id;
            const tutorId = sender.type === UserType.TUTOR ? sender.id : recipient.id;

            throw new ForbiddenException(`Student ${studentId} and tutor ${tutorId} are not mapped to each other`);
          }
        }

        throw new ForbiddenException('Users cannot chat with each other');
      }

      // Get or create conversation
      const conversation = await this.getOrCreateConversation(senderId, recipientId);

      // Create message
      const message = this.messageRepository.create({
        conversationId: conversation.id,
        senderId,
        recipientId,
        type,
        content,
        metadata,
        status: MessageStatus.SENT,
      });

      const savedMessage = await this.messageRepository.save(message);

      // Process attachments if any
      if (attachmentIds && attachmentIds.length > 0) {
        await this.processMessageAttachments(savedMessage.id, attachmentIds);
      }

      // Update conversation with last message info
      conversation.lastMessageAt = savedMessage.createdAt;
      conversation.lastMessageText = content;
      conversation.lastMessageSenderId = senderId;

      // Update unread counts
      if (conversation.participant1Id === recipientId) {
        conversation.participant1UnreadCount += 1;
      } else {
        conversation.participant2UnreadCount += 1;
      }

      await this.conversationRepository.save(conversation);

      // Get message with attachments
      const messageWithAttachments = await this.messageRepository.findOne({
        where: { id: savedMessage.id },
        relations: ['sender', 'attachments'],
      });

      // Send notification to recipient
      await this.sendMessageNotification(messageWithAttachments);

      return await this.mapMessageToDto(messageWithAttachments);
    } catch (error) {
      this.logger.error(`Error sending message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process message attachments
   * @param messageId Message ID
   * @param attachmentIds Attachment IDs (from message registry)
   */
  private async processMessageAttachments(messageId: string, attachmentIds: string[]): Promise<void> {
    try {
      // Get registry entries
      const registryEntries = await this.messageRegistryRepository.find({
        where: { id: In(attachmentIds), isTemporary: true },
      });

      if (registryEntries.length === 0) {
        return;
      }

      // Create message attachments
      const attachments = registryEntries.map((registry) => {
        return this.messageAttachmentRepository.create({
          messageId,
          filePath: registry.filePath,
          fileName: registry.fileName,
          mimeType: registry.mimeType,
          fileSize: registry.fileSize,
          thumbnailPath: registry.thumbnailPath,
        });
      });

      await this.messageAttachmentRepository.save(attachments);

      // Update registry entries to mark them as permanent
      await Promise.all(
        registryEntries.map(async (registry) => {
          registry.isTemporary = false;
          registry.messageId = messageId;
          return this.messageRegistryRepository.save(registry);
        }),
      );
    } catch (error) {
      this.logger.error(`Error processing message attachments: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send notification for a new message
   * @param message Message entity
   */
  private async sendMessageNotification(message: Message): Promise<void> {
    try {
      const sender = message.sender || (await this.userRepository.findOne({ where: { id: message.senderId } }));
      if (!sender) {
        return;
      }

      const notificationTitle = `New message from ${sender.name}`;
      let notificationContent = message.content;

      // Customize notification based on message type
      if (message.type === MessageType.IMAGE) {
        notificationContent = 'Sent you an image';
      } else if (message.type === MessageType.FILE) {
        notificationContent = 'Sent you a file';
      } else if (message.type === MessageType.QUIZ) {
        notificationContent = 'Sent you a quiz';
      }

      // Truncate content if too long
      if (notificationContent.length > 100) {
        notificationContent = notificationContent.substring(0, 97) + '...';
      }

      await this.notificationHelper.notify(message.recipientId, NotificationType.CHAT_MESSAGE, notificationTitle, notificationContent, {
        relatedEntityId: message.id,
        relatedEntityType: 'message',
        sendEmail: false,
        sendPush: true,
        sendInApp: true,
        sendMobile: true,
        sendSms: false,
        sendRealtime: true,
      });
    } catch (error) {
      this.logger.error(`Error sending message notification: ${error.message}`, error.stack);
    }
  }

  /**
   * Mark messages as read
   * @param conversationId Conversation ID
   * @param userId User ID (recipient)
   */
  async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      if (!conversation) {
        throw new NotFoundException('Conversation not found');
      }

      // Check if user is a participant
      if (conversation.participant1Id !== userId && conversation.participant2Id !== userId) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      // Update unread messages
      await this.messageRepository.update(
        {
          conversationId,
          recipientId: userId,
          status: MessageStatus.SENT,
        },
        {
          status: MessageStatus.READ,
          readAt: getCurrentUTCDate(),
        },
      );

      // Reset unread count for the user
      if (conversation.participant1Id === userId) {
        conversation.participant1UnreadCount = 0;
      } else {
        conversation.participant2UnreadCount = 0;
      }

      await this.conversationRepository.save(conversation);
    } catch (error) {
      this.logger.error(`Error marking messages as read: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark messages as delivered
   * @param conversationId Conversation ID
   * @param userId User ID (recipient)
   */
  async markMessagesAsDelivered(conversationId: string, userId: string): Promise<void> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      if (!conversation) {
        throw new NotFoundException('Conversation not found');
      }

      // Check if user is a participant
      if (conversation.participant1Id !== userId && conversation.participant2Id !== userId) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      // Update sent messages to delivered
      await this.messageRepository.update(
        {
          conversationId,
          recipientId: userId,
          status: MessageStatus.SENT,
        },
        {
          status: MessageStatus.DELIVERED,
          deliveredAt: getCurrentUTCDate(),
        },
      );
    } catch (error) {
      this.logger.error(`Error marking messages as delivered: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Upload a file for a message using FileRegistryService for consistency
   * @param userId User ID
   * @param file File to upload
   * @returns File upload response
   */
  async uploadFile(userId: string, file: MulterFile): Promise<ChatFileUploadResponseDto> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Use FileRegistryService for consistent upload handling
      const result = await this.fileRegistryService.uploadFile(FileEntityType.MESSAGE_ATTACHMENT, file, userId, { userId, isTemporary: true });

      // Generate consistent URLs using FileRegistryService
      const fileUrl = await this.fileRegistryService.getMessageAttachmentUrl(result.registry.id);
      const mediaControllerUrl = await this.fileRegistryService.getFileUrl(FileEntityType.MESSAGE_ATTACHMENT, result.registry.id);

      return {
        id: result.registry.id,
        filePath: mediaControllerUrl, // Use media controller URL instead of file system path
        fileName: result.registry.fileName,
        mimeType: result.registry.mimeType,
        fileSize: result.registry.fileSize,
        fileUrl,
      };
    } catch (error) {
      this.logger.error(`Error uploading file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get file by ID (deprecated - use media controller instead)
   * @param fileId File ID (registry ID)
   * @returns File buffer and metadata
   */
  async getFile(fileId: string): Promise<{ buffer: Buffer; fileName: string; mimeType: string }> {
    try {
      // Use FileRegistryService for consistent file handling
      return await this.fileRegistryService.getFileBuffer(FileEntityType.MESSAGE_ATTACHMENT, fileId);
    } catch (error) {
      this.logger.error(`Error getting file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get available chat contacts for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns List of users who can chat with the given user
   */
  async getChatContacts(userId: string, filter?: ContactFilterDto): Promise<ConversationParticipantDto[]> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      let contacts: User[] = [];

      // If user is admin, get all users
      if (user.type === UserType.ADMIN) {
        contacts = await this.userRepository.find({
          where: { id: Not(Equal(userId)) },
        });
      }
      // If user is student, get assigned tutors
      else if (user.type === UserType.STUDENT) {
        this.logger.log(`Getting tutors for student ${userId}`);
        const mappings = await this.studentTutorMappingRepository.find({
          where: { studentId: userId, status: MappingStatus.ACTIVE },
          relations: ['tutor'],
        });
        this.logger.log(`Found ${mappings.length} active tutor mappings for student ${userId}`);

        // Log each mapping for debugging
        mappings.forEach((mapping) => {
          this.logger.log(`Mapping: studentId=${mapping.studentId}, tutorId=${mapping.tutorId}, planFeatureId=${mapping.planFeatureId}, status=${mapping.status}`);
        });

        contacts = mappings.map((mapping) => mapping.tutor);

        // Also get student friends
        const friendships = await this.studentFriendshipRepository.find({
          where: [
            { requesterId: userId, status: FriendshipStatus.ACCEPTED },
            { requestedId: userId, status: FriendshipStatus.ACCEPTED },
          ],
          relations: ['requester', 'requested'],
        });

        // Add friends to contacts
        for (const friendship of friendships) {
          const friendId = friendship.requesterId === userId ? friendship.requestedId : friendship.requesterId;
          const friend = friendship.requesterId === userId ? friendship.requested : friendship.requester;

          // Check if friend is already in contacts
          if (!contacts.some((contact) => contact.id === friendId) && friend) {
            contacts.push(friend);
          }
        }
      }
      // If user is tutor, get assigned students
      else if (user.type === UserType.TUTOR) {
        this.logger.log(`Getting students for tutor ${userId}`);
        const mappings = await this.studentTutorMappingRepository.find({
          where: { tutorId: userId, status: MappingStatus.ACTIVE },
          relations: ['student'],
        });
        this.logger.log(`Found ${mappings.length} active student mappings for tutor ${userId}`);

        // Log each mapping for debugging
        mappings.forEach((mapping) => {
          this.logger.log(`Mapping: studentId=${mapping.studentId}, tutorId=${mapping.tutorId}, planFeatureId=${mapping.planFeatureId}, status=${mapping.status}`);
        });

        contacts = mappings.map((mapping) => mapping.student);
      }

      // Apply filters if provided
      if (filter) {
        if (filter.name) {
          contacts = contacts.filter((contact) => contact.name.toLowerCase().includes(filter.name.toLowerCase()));
        }
        if (filter.email) {
          contacts = contacts.filter((contact) => contact.email.toLowerCase().includes(filter.email.toLowerCase()));
        }
        if (filter.phone) {
          contacts = contacts.filter((contact) => contact.phoneNumber && contact.phoneNumber.includes(filter.phone));
        }
      }

      // Find or create conversations for each contact
      const contactDtos = await Promise.all(
        contacts.map(async (contact) => {
          try {
            // Find existing conversation
            let conversation = await this.conversationRepository.findOne({
              where: [
                { participant1Id: userId, participant2Id: contact.id },
                { participant1Id: contact.id, participant2Id: userId },
              ],
            });

            // If no conversation exists, create one
            if (!conversation) {
              // Check if users can chat
              const canChat = await this.canUsersChat(userId, contact.id);
              if (canChat) {
                conversation = await this.getOrCreateConversation(userId, contact.id);
                this.logger.log(`Created new conversation ${conversation.id} between users ${userId} and ${contact.id}`);
              } else {
                this.logger.warn(`Users ${userId} and ${contact.id} cannot chat with each other`);
                return null;
              }
            }

            // Get last message and time if conversation exists
            let lastMessage = null;
            let lastMessageTime = null;

            if (conversation) {
              lastMessage = conversation.lastMessageText;
              lastMessageTime = conversation.lastMessageAt;

              // If there's no last message in the conversation entity, try to fetch the latest message
              if (!lastMessage && conversation.id) {
                try {
                  const latestMessage = await this.messageRepository.findOne({
                    where: { conversationId: conversation.id },
                    order: { createdAt: 'DESC' },
                  });

                  if (latestMessage) {
                    lastMessage = latestMessage.content;
                    lastMessageTime = latestMessage.createdAt;
                  }
                } catch (error) {
                  this.logger.error(`Error fetching latest message for conversation ${conversation.id}: ${error.message}`, error.stack);
                }
              }
            }

            return {
              id: contact.id,
              name: contact.name,
              userId: contact.userId,
              email: contact.email,
              type: contact.type,
              profilePicture: contact.profilePicture,
              conversationId: conversation?.id, // Include conversation ID
              lastMessage: lastMessage, // Include last message
              lastMessageTime: lastMessageTime, // Include last message time
            };
          } catch (error) {
            this.logger.error(`Error processing contact ${contact.id}: ${error.message}`, error.stack);
            return null;
          }
        }),
      );

      // Filter out null values (contacts that couldn't get a conversation)
      return contactDtos.filter((dto) => dto !== null);
    } catch (error) {
      this.logger.error(`Error getting chat contacts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Map conversation entity to DTO
   * @param conversation Conversation entity
   * @param currentUserId Current user ID
   * @returns Conversation DTO
   */
  private async mapConversationToDto(conversation: Conversation, currentUserId: string): Promise<ConversationDto> {
    // Determine the other participant
    const otherParticipantId = conversation.participant1Id === currentUserId ? conversation.participant2Id : conversation.participant1Id;

    // Get other participant
    const otherParticipant = conversation.participant1Id === currentUserId ? conversation.participant2 : conversation.participant1;

    // If other participant is not loaded, fetch it
    const participant = otherParticipant || (await this.userRepository.findOne({ where: { id: otherParticipantId } }));

    if (!participant) {
      throw new NotFoundException('Participant not found');
    }

    // Determine unread count for current user
    const unreadCount = conversation.participant1Id === currentUserId ? conversation.participant1UnreadCount : conversation.participant2UnreadCount;

    return {
      id: conversation.id,
      type: conversation.type,
      status: conversation.status,
      participant: {
        id: participant.id,
        name: participant.name,
        userId: participant.userId,
        email: participant.email,
        type: participant.type,
        profilePicture: participant.profilePicture,
        unreadCount,
      },
      lastMessageAt: conversation.lastMessageAt,
      lastMessageText: conversation.lastMessageText,
      lastMessageSenderId: conversation.lastMessageSenderId,
      unreadCount,
      createdAt: conversation.createdAt,
    };
  }

  /**
   * Map message entity to DTO with consistent file URLs
   * @param message Message entity
   * @returns Message DTO
   */
  private async mapMessageToDto(message: Message): Promise<MessageDto> {
    const sender = message.sender;

    // Map attachments to DTOs first
    const attachmentDtos =
      message.attachments?.map((attachment) => ({
        id: attachment.id,
        filePath: attachment.filePath, // Will be replaced with media controller URL
        fileName: attachment.fileName,
        mimeType: attachment.mimeType,
        fileSize: attachment.fileSize,
        thumbnailPath: attachment.thumbnailPath,
        fileUrl: '', // Will be set below
        thumbnailUrl: undefined, // Will be set below
      })) || [];

    // Generate file URLs for all attachments (same pattern as shop items)
    await Promise.all(
      attachmentDtos.map(async (dto, index) => {
        const attachment = message.attachments[index];
        try {
          // Find the corresponding MessageRegistry entry for this attachment
          const registryEntry = await this.messageRegistryRepository.findOne({
            where: {
              messageId: message.id,
              fileName: attachment.fileName,
              isTemporary: false,
            },
          });

          if (registryEntry) {
            // Use FileRegistryService to get the URL (same as shop items)
            const fileUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.MESSAGE_ATTACHMENT, registryEntry.id);

            if (fileUrl) {
              dto.id = registryEntry.id; // Use registry ID
              dto.filePath = fileUrl; // Set media controller URL
              dto.fileUrl = fileUrl; // Set same URL for fileUrl
              dto.thumbnailUrl = attachment.thumbnailPath ? fileUrl : undefined;
            }
          } else {
            this.logger.error(`Registry entry not found for attachment ${attachment.id} in message ${message.id}. This indicates a data integrity issue.`);
          }
        } catch (error) {
          this.logger.error(`Error generating file URL for attachment ${attachment.id}: ${error.message}`, error.stack);
        }
      }),
    );

    return {
      id: message.id,
      conversationId: message.conversationId,
      senderId: message.senderId,
      senderName: sender?.name || 'Unknown',
      senderProfilePicture: sender?.profilePicture,
      recipientId: message.recipientId,
      type: message.type,
      content: message.content,
      status: message.status,
      readAt: message.readAt,
      deliveredAt: message.deliveredAt,
      metadata: message.metadata,
      attachments: attachmentDtos,
      createdAt: message.createdAt,
    };
  }

  /**
   * Ensure an admin is added as a participant in an admin conversation
   * @param conversationId The conversation ID
   * @param adminId The admin ID
   */
  private async ensureAdminParticipant(conversationId: string, adminId: string): Promise<void> {
    const existingParticipant = await this.adminConversationParticipantRepository.findOne({
      where: { conversationId, adminId },
    });

    if (!existingParticipant) {
      const adminParticipant = this.adminConversationParticipantRepository.create({
        conversationId,
        adminId,
        isActive: true,
        lastAccessedAt: new Date(),
      });

      await this.adminConversationParticipantRepository.save(adminParticipant);
      this.logger.log(`Added admin ${adminId} as participant in conversation ${conversationId}`);
    } else if (!existingParticipant.isActive) {
      // Reactivate if inactive
      await this.adminConversationParticipantRepository.update(
        { conversationId, adminId },
        { isActive: true, lastAccessedAt: new Date() },
      );
      this.logger.log(`Reactivated admin ${adminId} in conversation ${conversationId}`);
    }
  }
}
