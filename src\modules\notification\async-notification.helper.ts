import { Injectable, Logger } from '@nestjs/common';
import { NotificationHelperService } from './notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { NotificationChannel } from '../../database/entities/notification-delivery.entity';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';

interface NotificationPayload {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  channels?: {
    sendRealtime?: boolean;
    sendInApp?: boolean;
    sendPush?: boolean;
    sendEmail?: boolean;
    sendSms?: boolean;
  };
}

@Injectable()
export class AsyncNotificationHelper {
  private readonly logger = new Logger(AsyncNotificationHelper.name);

  constructor(
    @InjectQueue('notifications')
    private readonly notificationsQueue: Queue<NotificationPayload>,
    private readonly notificationHelper: NotificationHelperService,
  ) {}

  /**
   * Queue a notification to be sent asynchronously
   */
  async queueNotification(payload: NotificationPayload): Promise<void> {
    try {
      await this.notificationsQueue.add('send-notification', payload, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: true,
      });

      // If realtime notification is requested, send it immediately
      if (payload.channels?.sendRealtime) {
        await this.notificationHelper.notify(
          payload.userId,
          payload.type,
          payload.title,
          payload.message,
          {
            ...payload.data,
            sendRealtime: true,
            sendInApp: false,
            sendPush: false,
            sendEmail: false,
            sendSms: false,
          }
        );
      }
    } catch (error) {
      this.logger.error(`Failed to queue notification: ${error.message}`, error.stack);
      // Fallback to synchronous notification if queueing fails
      await this.notificationHelper.notify(
        payload.userId,
        payload.type,
        payload.title,
        payload.message,
        {
          ...payload.data,
          ...payload.channels
        }
      );
    }
  }

  /**
   * Process a queued notification
   */
  async processNotification(payload: NotificationPayload): Promise<void> {
    try {
      // Skip realtime notification as it was already sent
      const channels = {
        ...payload.channels,
        sendRealtime: false,
      };

      await this.notificationHelper.notify(
        payload.userId,
        payload.type,
        payload.title,
        payload.message,
        {
          ...payload.data,
          ...channels
        }
      );
    } catch (error) {
      this.logger.error(`Failed to process notification: ${error.message}`, error.stack);
      throw error; // Re-throw to trigger bull retry mechanism
    }
  }
}
