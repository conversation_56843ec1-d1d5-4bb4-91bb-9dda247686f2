import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { Conversation } from './conversation.entity';
import { MessageAttachment } from './message-attachment.entity';

/**
 * Status of the message
 * @enum {string}
 */
export enum MessageStatus {
  /** Message is sent */
  SENT = 'sent',
  /** Message is delivered */
  DELIVERED = 'delivered',
  /** Message is read */
  READ = 'read',
  /** Message is deleted */
  DELETED = 'deleted',
}

/**
 * Type of the message
 * @enum {string}
 */
export enum MessageType {
  /** Text message */
  TEXT = 'text',
  /** Image message */
  IMAGE = 'image',
  /** File message */
  FILE = 'file',
  /** Quiz deep link message */
  QUIZ = 'quiz',
  /** System message */
  SYSTEM = 'system',
}

@Entity()
export class Message extends AuditableBaseEntity {
  @Column({ name: 'conversation_id' })
  conversationId: string;

  @ManyToOne(() => Conversation, (conversation) => conversation.messages)
  @JoinColumn({ name: 'conversation_id' })
  conversation: Conversation;

  @Column({ name: 'sender_id' })
  senderId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'sender_id' })
  sender: User;

  @Column({ name: 'recipient_id' })
  recipientId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'recipient_id' })
  recipient: User;

  @Column({
    name: 'type',
    type: 'enum',
    enum: MessageType,
    default: MessageType.TEXT,
  })
  type: MessageType;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: MessageStatus,
    default: MessageStatus.SENT,
  })
  status: MessageStatus;

  @Column({ name: 'read_at', nullable: true })
  readAt: Date;

  @Column({ name: 'delivered_at', nullable: true })
  deliveredAt: Date;

  @Column({ name: 'metadata', type: 'json', nullable: true })
  metadata: any;

  @OneToMany(() => MessageAttachment, (attachment) => attachment.message)
  attachments: MessageAttachment[];
}
