import { Injectable, BadRequestException, NotFoundException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { WaterfallSet } from '../../../database/entities/waterfall-set.entity';
import { WaterfallQuestion } from '../../../database/entities/waterfall-question.entity';
import { WaterfallParticipation } from '../../../database/entities/waterfall-participation.entity';
import { WaterfallAnswer } from '../../../database/entities/waterfall-answer.entity';
import { CreateWaterfallSetDto, UpdateWaterfallSetDto, WaterfallFilterDto, WaterfallSetResponseDto, ToggleWaterfallStatusDto } from '../../../database/models/waterfall/waterfall-set.dto';

import { CreateWaterfallQuestionsDto, UpdateWaterfallQuestionDto, WaterfallQuestionResponseDto, ToggleWaterfallQuestionStatusDto } from '../../../database/models/waterfall/waterfall-question.dto';

import { PagedListDto } from '../../../common/models/paged-list.dto';
import { WaterfallSetFullResponseDto } from '../../../database/models/waterfall/waterfall-response.dto';

import {
  WaterfallParticipantsQueryDto,
  WaterfallParticipantsResponseDto,
  WaterfallParticipantDto,
  WaterfallStudentParticipationQueryDto,
  WaterfallStudentParticipationResponseDto,
  WaterfallStudentParticipationDto,
  ParticipantsSortField,
  ParticipationSortField,
  SortDirection,
} from '../../../database/models/waterfall/waterfall-participants.dto';
// Note: User entity is referenced in raw SQL queries but doesn't need to be imported

interface ValidationError {
  index: number;
  errors: string[];
}

interface QuestionsValidationResult {
  validQuestions: CreateWaterfallQuestionsDto[];
  errors: ValidationError[];
}

@Injectable()
export class WaterfallAdminService {
  private readonly logger = new Logger(WaterfallAdminService.name);

  constructor(
    @InjectRepository(WaterfallSet)
    private readonly setRepository: Repository<WaterfallSet>,
    @InjectRepository(WaterfallQuestion)
    private readonly questionRepository: Repository<WaterfallQuestion>,
    @InjectRepository(WaterfallParticipation)
    private readonly participationRepository: Repository<WaterfallParticipation>,
    @InjectRepository(WaterfallAnswer)
    private readonly answerRepository: Repository<WaterfallAnswer>,
    private readonly dataSource: DataSource,
  ) {}

  private validateQuestion(question: CreateWaterfallQuestionsDto, _index: number): string[] {
    const errors: string[] = [];
    const gapCount = (question.question_text_plain.match(/\[\[gap\]\]/g) || []).length;

    if (gapCount === 0) {
      errors.push('Question must contain at least one [[gap]] marker');
    }

    if (gapCount !== question.correct_answers.length) {
      errors.push(`Question has ${gapCount} [[gap]] marker(s) but ${question.correct_answers.length} correct answer(s)`);
    }

    // Validate that all correct answers are included in the options
    if (question.options && question.correct_answers) {
      const missingOptions = question.correct_answers.filter((answer) => !question.options.includes(answer));

      if (missingOptions.length > 0) {
        errors.push(`The following correct answers are not included in the options: ${missingOptions.join(', ')}`);
      }
    }

    return errors;
  }

  private validateQuestions(questions: CreateWaterfallQuestionsDto[]): QuestionsValidationResult {
    const validQuestions: CreateWaterfallQuestionsDto[] = [];
    const errors: ValidationError[] = [];

    questions.forEach((question, index) => {
      const questionErrors = this.validateQuestion(question, index);

      if (questionErrors.length === 0) {
        validQuestions.push(question);
      } else {
        errors.push({
          index,
          errors: questionErrors,
        });
      }
    });

    return { validQuestions, errors };
  }

  /**
   * Check if a set has any participation records
   * @param setId The ID of the set to check
   * @returns True if the set has participation records, false otherwise
   */
  private async hasParticipation(setId: string): Promise<boolean> {
    const count = await this.participationRepository.count({
      where: { setId },
    });
    return count > 0;
  }

  /**
   * Check if a question has any answer records
   * @param questionId The ID of the question to check
   * @returns True if the question has answer records, false otherwise
   */
  private async hasAnswers(questionId: string): Promise<boolean> {
    const count = await this.answerRepository.count({
      where: { questionId },
    });
    return count > 0;
  }

  async getAllSets(filterDto: WaterfallFilterDto): Promise<PagedListDto<WaterfallSetResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC', title } = filterDto;

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    const queryBuilder = this.setRepository.createQueryBuilder('set');

    if (title) {
      queryBuilder.where('set.title ILIKE :title', { title: `%${title}%` });
    }

    // Apply sorting (with validation to prevent SQL injection)
    const allowedSortFields = ['title', 'totalScore', 'totalQuestions', 'createdAt', 'updatedAt'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';

    queryBuilder.orderBy(`set.${validSortBy}`, sortDirection);

    queryBuilder.skip(skip).take(limit);

    // Execute the query
    const [sets, totalCount] = await queryBuilder.getManyAndCount();

    // Map to response DTOs
    const setDtos = sets.map((set) => this.mapToResponseDto(set));

    // Return paged list
    return new PagedListDto<WaterfallSetResponseDto>(setDtos, totalCount);
  }

  private mapToResponseDto(set: WaterfallSet): WaterfallSetResponseDto {
    return {
      id: set.id,
      title: set.title,
      total_score: set.totalScore,
      total_questions: set.totalQuestions,
      is_active: set.isActive,
      created_at: set.createdAt,
      updated_at: set.updatedAt,
    };
  }

  async getSetById(setId: string): Promise<WaterfallSet> {
    const set = await this.setRepository.findOne({ where: { id: setId } });
    if (!set) {
      throw new NotFoundException(`Waterfall set with ID ${setId} not found`);
    }
    return set;
  }

  async createSet(dto: CreateWaterfallSetDto): Promise<WaterfallSet> {
    try {
      // Validate input
      if (dto.total_score <= 0) {
        this.logger.warn(`Invalid total score: ${dto.total_score}`);
        throw new BadRequestException('Total score must be greater than 0');
      }

      // Check if a set with the same title already exists
      const existingSet = await this.setRepository.findOne({ where: { title: dto.title } });
      if (existingSet) {
        this.logger.warn(`Attempted to create a duplicate set with title: "${dto.title}"`);
        throw new BadRequestException(`A waterfall set with title "${dto.title}" already exists`);
      }

      const set = this.setRepository.create({
        title: dto.title,
        totalScore: dto.total_score,
        totalQuestions: 0,
        isActive: true,
      });

      const savedSet = await this.setRepository.save(set);
      return savedSet;
    } catch (error) {
      this.logger.error(`Failed to create waterfall set: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException
      throw new BadRequestException(`Failed to create waterfall set: ${error.message}`);
    }
  }

  /**
   * Create a single question for a waterfall set
   * @param setId The ID of the set to add the question to
   * @param question The question data to create
   * @returns The created question
   */
  async createQuestion(setId: string, question: CreateWaterfallQuestionsDto): Promise<WaterfallQuestion> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const set = await this.setRepository.findOne({ where: { id: setId } });
      if (!set) {
        this.logger.warn(`Waterfall set with ID ${setId} not found`);
        throw new NotFoundException(`The requested game set was not found. Please check and try again.`);
      }

      const hasParticipation = await this.hasParticipation(setId);
      if (hasParticipation) {
        this.logger.warn(`Attempted to add question to waterfall set with ID ${setId} that has participation records`);
        throw new ForbiddenException(`This game set has already been played by students. For data integrity, questions cannot be added to sets that have participation records.`);
      }

      // Validate the question
      const validationErrors = this.validateQuestion(question, 0);
      if (validationErrors.length > 0) {
        this.logger.warn(`Invalid question for set ID ${setId}: ${validationErrors.join(', ')}`);
        throw new BadRequestException({
          message: 'Invalid question',
          error: {
            type: 'ValidationError',
            details: validationErrors,
          },
        });
      }

      const questionEntity = this.questionRepository.create({
        questionText: question.question_text,
        questionTextPlain: question.question_text_plain,
        correctAnswers: question.correct_answers,
        options: question.options,
        timeLimitInSeconds: question.time_limit_in_seconds,
        level: question.level,
        setId: setId,
      });

      const savedQuestion = await queryRunner.manager.save(questionEntity);

      set.totalQuestions += 1;
      await queryRunner.manager.save(set);

      await queryRunner.commitTransaction();

      return savedQuestion;
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();

      this.logger.error(`Failed to create question for set ID ${setId}: ${error.message}`, error.stack);

      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async createQuestions(setId: string, questions: CreateWaterfallQuestionsDto[]): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const set = await this.setRepository.findOne({ where: { id: setId } });
      if (!set) {
        this.logger.warn(`Waterfall set with ID ${setId} not found`);
        throw new NotFoundException(`The requested game set was not found. Please check and try again.`);
      }

      if (questions.length === 0) {
        this.logger.warn(`Empty questions array provided for set ID: ${setId}`);
        throw new BadRequestException('No questions provided');
      }

      const { validQuestions, errors } = this.validateQuestions(questions);

      if (validQuestions.length === 0) {
        const errorDetails = errors.reduce(
          (acc, error) => {
            acc[`question${error.index}`] = error.errors;
            return acc;
          },
          {} as Record<string, string[]>,
        );

        this.logger.warn(`No valid questions to create for set ID: ${setId}. Errors: ${JSON.stringify(errorDetails)}`);

        throw new BadRequestException({
          message: 'No valid questions to create',
          error: {
            type: 'ValidationError',
            details: errorDetails,
          },
        });
      }

      // Create question entities
      const questionEntities = validQuestions.map((question) =>
        this.questionRepository.create({
          questionText: question.question_text,
          questionTextPlain: question.question_text_plain,
          correctAnswers: question.correct_answers,
          options: question.options,
          timeLimitInSeconds: question.time_limit_in_seconds,
          level: question.level,
          setId: setId,
        }),
      );

      // Save questions in transaction
      await queryRunner.manager.save(questionEntities);

      // Update set's question count
      set.totalQuestions += validQuestions.length;
      await queryRunner.manager.save(set);

      // Handle partial success before committing the transaction
      if (errors.length > 0) {
        // Commit the transaction first to save the valid questions
        await queryRunner.commitTransaction();

        const errorDetails = errors.reduce(
          (acc, error) => {
            acc[`question${error.index}`] = error.errors;
            return acc;
          },
          {} as Record<string, string[]>,
        );

        this.logger.warn(`Partial success: ${validQuestions.length}/${questions.length} questions created. Errors: ${JSON.stringify(errorDetails)}`);

        // Return a BadRequestException without going through the catch block
        return Promise.reject(
          new BadRequestException({
            message: `Created ${validQuestions.length} questions successfully, but ${errors.length} questions had validation errors`,
            error: {
              type: 'PartialValidationError',
              details: errorDetails,
            },
            partialSuccess: {
              successCount: validQuestions.length,
              totalCount: questions.length,
            },
          }),
        );
      }

      // If no errors, commit the transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      this.logger.error(`Failed to create questions for set ID ${setId}: ${error.message}`, error.stack);
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();

      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Delete a waterfall set and all its associated questions
   * @param id The ID of the set to delete
   */
  async deleteSet(id: string): Promise<void> {
    // Check if set exists
    const set = await this.setRepository.findOne({ where: { id } });
    if (!set) {
      this.logger.warn(`Attempted to delete non-existent waterfall set with ID: ${id}`);
      throw new NotFoundException(`The game set you're trying to delete was not found. Please check and try again.`);
    }

    // Check if the set has any participation records
    const hasParticipation = await this.hasParticipation(id);
    if (hasParticipation) {
      this.logger.warn(`Attempted to delete waterfall set with ID ${id} that has participation records`);
      throw new ForbiddenException(`This game set has already been played by students. For data integrity, sets with participation records cannot be deleted.`);
    }

    try {
      // Delete the set (cascade will handle questions)
      await this.setRepository.remove(set);
      this.logger.log(`Successfully deleted waterfall set with ID: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete waterfall set with ID ${id}: ${error.message}`, error.stack);
      throw new BadRequestException(`We couldn't delete this game set. Please try again later.`);
    }
  }

  /**
   * Update a waterfall set by ID
   * @param id The ID of the set to update
   * @param dto The data to update the set with
   * @returns The updated waterfall set
   */
  async updateSet(id: string, dto: UpdateWaterfallSetDto): Promise<WaterfallSet> {
    try {
      // Find the set
      const set = await this.setRepository.findOne({ where: { id } });
      if (!set) {
        this.logger.warn(`Waterfall set with ID ${id} not found`);
        throw new NotFoundException(`The game set you're trying to update was not found. Please check and try again.`);
      }

      // Check if the set has any participation records
      const hasParticipation = await this.hasParticipation(id);
      if (hasParticipation) {
        this.logger.warn(`Attempted to update waterfall set with ID ${id} that has participation records`);
        throw new ForbiddenException(`This game set has already been played by students. For data integrity, sets with participation records cannot be updated.`);
      }

      // Validate input
      if (dto.total_score !== undefined && dto.total_score <= 0) {
        this.logger.warn(`Invalid total score: ${dto.total_score}`);
        throw new BadRequestException('Total score must be greater than 0');
      }

      // Check if a set with the same title already exists (only if title is being updated)
      if (dto.title && dto.title !== set.title) {
        const existingSet = await this.setRepository.findOne({ where: { title: dto.title } });
        if (existingSet && existingSet.id !== id) {
          this.logger.warn(`Attempted to update to a duplicate title: "${dto.title}"`);
          throw new BadRequestException(`A waterfall set with title "${dto.title}" already exists`);
        }
      }

      // Update the set - ignore null values and empty strings to keep previous values
      if (dto.title !== undefined && dto.title !== null && dto.title.trim() !== '') {
        set.title = dto.title;
      }

      if (dto.total_score !== undefined && dto.total_score !== null) {
        set.totalScore = dto.total_score;
      }

      // Save the updated set
      const updatedSet = await this.setRepository.save(set);
      return updatedSet;
    } catch (error) {
      this.logger.error(`Failed to update waterfall set with ID ${id}: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof BadRequestException || error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException
      throw new BadRequestException(`We couldn't update this game set. Please try again later.`);
    }
  }

  /**
   * Delete a specific waterfall question by ID
   * @param questionId The ID of the question to delete
   */
  async deleteQuestion(questionId: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Find the question
      const question = await this.questionRepository.findOne({ where: { id: questionId } });
      if (!question) {
        this.logger.warn(`Attempted to delete non-existent waterfall question with ID: ${questionId}`);
        throw new NotFoundException(`The question you're trying to delete was not found. Please check and try again.`);
      }

      // Find the set to update its question count
      const set = await this.setRepository.findOne({ where: { id: question.setId } });
      if (!set) {
        this.logger.warn(`Waterfall set with ID ${question.setId} not found`);
        throw new NotFoundException(`The game set containing this question was not found. Please check and try again.`);
      }

      // Check if the question has any answer records
      const hasAnswers = await this.hasAnswers(questionId);
      if (hasAnswers) {
        this.logger.warn(`Attempted to delete waterfall question with ID ${questionId} that has answer records`);
        throw new ForbiddenException(`This question has already been answered by students. For data integrity, questions with answer records cannot be deleted.`);
      }

      // Delete the question
      await queryRunner.manager.remove(question);

      // Update the set's question count
      set.totalQuestions = Math.max(0, set.totalQuestions - 1);
      await queryRunner.manager.save(set);

      // Commit the transaction
      await queryRunner.commitTransaction();

      this.logger.log(`Successfully deleted waterfall question with ID: ${questionId}`);
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();

      this.logger.error(`Failed to delete waterfall question with ID ${questionId}: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException
      throw new BadRequestException(`We couldn't delete this question. Please try again later.`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Update a specific waterfall question by ID
   * @param questionId The ID of the question to update
   * @param dto The data to update the question with
   * @returns The updated waterfall question
   */
  async updateQuestion(questionId: string, dto: UpdateWaterfallQuestionDto): Promise<WaterfallQuestion> {
    try {
      // Find the question
      const question = await this.questionRepository.findOne({ where: { id: questionId } });
      if (!question) {
        this.logger.warn(`Waterfall question with ID ${questionId} not found`);
        throw new NotFoundException(`The question you're trying to update was not found. Please check and try again.`);
      }

      // Get the set to update its question count if needed
      const set = await this.setRepository.findOne({ where: { id: question.setId } });
      if (!set) {
        this.logger.warn(`Waterfall set with ID ${question.setId} not found`);
        throw new NotFoundException(`The game set containing this question was not found. Please check and try again.`);
      }

      // Check if the question has any answer records
      const hasAnswers = await this.hasAnswers(questionId);
      if (hasAnswers) {
        this.logger.warn(`Attempted to update waterfall question with ID ${questionId} that has answer records`);
        throw new ForbiddenException(`This question has already been answered by students. For data integrity, questions with answer records cannot be updated.`);
      }

      // Create a copy of the question for validation
      const validationQuestion: CreateWaterfallQuestionsDto = {
        question_text: dto.question_text !== undefined ? dto.question_text : question.questionText,
        question_text_plain: dto.question_text_plain !== undefined ? dto.question_text_plain : question.questionTextPlain,
        correct_answers: dto.correct_answers !== undefined ? dto.correct_answers : question.correctAnswers,
        options: dto.options !== undefined ? dto.options : question.options,
      };

      // Validate the question
      const validationErrors = this.validateQuestion(validationQuestion, 0);
      if (validationErrors.length > 0) {
        this.logger.warn(`Invalid question update for ID ${questionId}: ${validationErrors.join(', ')}`);
        throw new BadRequestException({
          message: 'Invalid question update',
          error: {
            type: 'ValidationError',
            details: validationErrors,
          },
        });
      }

      // Update the question - ignore null values and empty strings to keep previous values
      if (dto.question_text !== undefined && dto.question_text !== null && dto.question_text.trim() !== '') {
        question.questionText = dto.question_text;
      }

      if (dto.question_text_plain !== undefined && dto.question_text_plain !== null && dto.question_text_plain.trim() !== '') {
        question.questionTextPlain = dto.question_text_plain;
      }

      if (dto.correct_answers !== undefined && dto.correct_answers !== null && dto.correct_answers.length > 0) {
        // Filter out any null or empty string values in the array
        const validAnswers = dto.correct_answers.filter((answer) => answer !== null && answer.trim() !== '');
        if (validAnswers.length > 0) {
          question.correctAnswers = validAnswers;
        }
      }

      if (dto.options !== undefined && dto.options !== null && dto.options.length > 0) {
        // Filter out any null or empty string values in the array
        const validOptions = dto.options.filter((option) => option !== null && option.trim() !== '');
        if (validOptions.length > 0) {
          question.options = validOptions;
        }
      }

      if (dto.time_limit_in_seconds !== undefined && dto.time_limit_in_seconds !== null) {
        question.timeLimitInSeconds = dto.time_limit_in_seconds;
      }

      if (dto.level !== undefined && dto.level !== null) {
        question.level = dto.level;
      }

      // Save the updated question
      const updatedQuestion = await this.questionRepository.save(question);
      return updatedQuestion;
    } catch (error) {
      this.logger.error(`Failed to update waterfall question with ID ${questionId}: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof BadRequestException || error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException
      throw new BadRequestException(`We couldn't update this question. Please try again later.`);
    }
  }

  /**
   * Get a waterfall set by ID with all its questions
   * @param id The ID of the set to retrieve
   * @returns The waterfall set with all its questions
   */
  async getSetWithQuestions(id: string): Promise<WaterfallSetFullResponseDto> {
    try {
      // Find the set with its questions
      const set = await this.setRepository.findOne({
        where: { id },
        relations: ['questions'],
      });

      if (!set) {
        this.logger.warn(`Waterfall set with ID ${id} not found`);
        throw new NotFoundException(`The requested game set was not found. Please check and try again.`);
      }

      // Map the questions to the response DTO format
      const questionDtos: WaterfallQuestionResponseDto[] = set.questions.map((question) => ({
        id: question.id,
        question_text: question.questionText,
        question_text_plain: question.questionTextPlain,
        correct_answers: question.correctAnswers,
        options: question.options,
        created_at: question.createdAt,
        updated_at: question.updatedAt,
        time_limit_in_seconds: question.timeLimitInSeconds,
        level: question.level,
        is_active: question.isActive,
      }));

      // Return the full set response
      return {
        id: set.id,
        title: set.title,
        total_score: set.totalScore,
        total_questions: set.totalQuestions,
        created_at: set.createdAt,
        updated_at: set.updatedAt,
        questions: questionDtos,
      };
    } catch (error) {
      this.logger.error(`Failed to get waterfall set with questions for ID ${id}: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException
      throw new BadRequestException(`We couldn't retrieve this game set and its questions. Please try again later.`);
    }
  }

  /**
   * Get participants for all waterfall sets with aggregated data
   * @param queryDto Query parameters for pagination, search, and sorting
   * @returns A paginated list of participants with their aggregated performance data across all sets
   */
  async getAllParticipants(queryDto: WaterfallParticipantsQueryDto): Promise<WaterfallParticipantsResponseDto> {
    try {
      // Extract query parameters with defaults
      const { page = 1, limit = 10, search, sortBy = ParticipantsSortField.LATEST_PARTICIPATION } = queryDto;
      const skip = (page - 1) * limit;

      // Create a query that aggregates data by student across all sets
      const query = this.participationRepository
        .createQueryBuilder('participation')
        .select('participation.studentId', 'student_id')
        .addSelect('user.name', 'name')
        .addSelect('user.email', 'email')
        .addSelect('COUNT(participation.id)', 'total_attempts')
        .addSelect('SUM(participation.score)', 'total_marks')
        .addSelect('MAX(participation.createdAt)', 'last_participation_date')
        .innerJoin('user', 'user', 'participation.studentId = user.id')
        .groupBy('participation.studentId')
        .addGroupBy('user.name')
        .addGroupBy('user.email');

      if (search) {
        query.andWhere('(user.name ILIKE :search OR user.email ILIKE :search)', { search: `%${search}%` });
      }

      if (sortBy === ParticipantsSortField.HIGHEST_SCORE) {
        query.orderBy('total_marks', 'DESC');
      } else {
        // Default sort by latest participation date
        query.orderBy('last_participation_date', 'DESC');
      }

      query.offset(skip).limit(limit);

      // Execute the query
      const [participants, totalCount] = await Promise.all([
        query.getRawMany(),
        this.participationRepository
          .createQueryBuilder('participation')
          .select('COUNT(DISTINCT participation.studentId)', 'count')
          .innerJoin('user', 'user', 'participation.studentId = user.id')
          .where(search ? '(user.name ILIKE :search OR user.email ILIKE :search)' : '1=1', search ? { search: `%${search}%` } : {})
          .getRawOne()
          .then((result) => parseInt(result.count)),
      ]);

      // Map the raw results to the DTO format
      const participantDtos: WaterfallParticipantDto[] = participants.map((p) => ({
        student_id: p.student_id,
        name: p.name,
        email: p.email,
        total_attempts: parseInt(p.total_attempts),
        total_marks: parseInt(p.total_marks),
        last_participation_date: new Date(p.last_participation_date),
      }));

      const totalPages = Math.ceil(totalCount / limit);

      return {
        participants: participantDtos,
        total_items: totalCount,
        total_pages: totalPages,
        current_page: page,
        page_size: limit,
      };
    } catch (error) {
      this.logger.error(`Failed to get participants: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException with a user-friendly message
      throw new BadRequestException("We couldn't retrieve the participant list at this time. Please try again later.");
    }
  }

  /**
   * Get a specific student's participation history
   * @param studentId The ID of the student
   * @param queryDto Query parameters for pagination and sorting
   * @returns A paginated list of the student's participation records
   */
  async getStudentParticipationHistory(studentId: string, queryDto: WaterfallStudentParticipationQueryDto): Promise<WaterfallStudentParticipationResponseDto> {
    try {
      const student = await this.dataSource.getRepository('user').createQueryBuilder('user').where('user.id = :studentId', { studentId }).getOne();

      if (!student) {
        this.logger.warn(`Student with ID ${studentId} not found`);
        throw new NotFoundException(`The requested student was not found. Please check and try again.`);
      }

      // Extract query parameters with defaults
      const { page = 1, limit = 10, sortBy = ParticipationSortField.PARTICIPATED_AT, sortDirection = SortDirection.DESC } = queryDto;
      const skip = (page - 1) * limit;

      const query = this.participationRepository
        .createQueryBuilder('participation')
        .select('participation.id', 'participation_id')
        .addSelect('participation.setId', 'set_id')
        .addSelect('set.title', 'set_title')
        .addSelect('set.totalQuestions', 'total_questions')
        .addSelect('set.totalScore', 'total_score')
        .addSelect('participation.totalCorrectAnswers', 'correct_answers')
        .addSelect('participation.score', 'score')
        .addSelect('participation.createdAt', 'participated_at')
        .innerJoin('waterfall_set', 'set', 'participation.setId = set.id')
        .where('participation.studentId = :studentId', { studentId });

      if (sortBy === ParticipationSortField.SCORE) {
        query.orderBy('participation.score', sortDirection);
      } else {
        // Default sort by participation date
        query.orderBy('participation.createdAt', sortDirection);
      }

      query.offset(skip).limit(limit);

      // Execute the query
      const [participations, totalCount] = await Promise.all([query.getRawMany(), query.getCount()]);

      // Map the raw results to the DTO format
      const participationDtos: WaterfallStudentParticipationDto[] = participations.map((p) => ({
        participation_id: p.participation_id,
        set_id: p.set_id,
        set_title: p.set_title,
        total_questions: parseInt(p.total_questions),
        total_score: parseInt(p.total_score),
        correct_answers: parseInt(p.correct_answers),
        score: parseInt(p.score),
        participated_at: new Date(p.participated_at),
      }));

      const totalPages = Math.ceil(totalCount / limit);

      return {
        student_id: studentId,
        name: student.name,
        email: student.email,
        participation_records: participationDtos,
        total_items: totalCount,
        total_pages: totalPages,
        current_page: page,
        page_size: limit,
      };
    } catch (error) {
      this.logger.error(`Failed to get participation history for student ${studentId}: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException with a user-friendly message
      throw new BadRequestException("We couldn't retrieve this student's participation history at this time. Please try again later.");
    }
  }

  /**
   * Toggle waterfall set active status
   */
  async toggleWaterfallSetStatus(id: string, dto: ToggleWaterfallStatusDto): Promise<WaterfallSetResponseDto> {
    const waterfallSet = await this.setRepository.findOne({
      where: { id },
    });

    if (!waterfallSet) {
      throw new NotFoundException('Waterfall set not found');
    }

    try {
      // Update only the active status
      waterfallSet.isActive = dto.is_active;
      await this.setRepository.save(waterfallSet);

      return {
        id: waterfallSet.id,
        title: waterfallSet.title,
        total_score: waterfallSet.totalScore,
        total_questions: waterfallSet.totalQuestions,
        is_active: waterfallSet.isActive,
        created_at: waterfallSet.createdAt,
        updated_at: waterfallSet.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Failed to toggle waterfall set status with ID ${id}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not update waterfall set status. Please try again later.');
    }
  }

  /**
   * Toggle the active status of a waterfall question
   */
  async toggleWaterfallQuestionStatus(setId: string, questionId: string, dto: ToggleWaterfallQuestionStatusDto): Promise<WaterfallQuestionResponseDto> {
    this.logger.log(`Toggling waterfall question status for question ID: ${questionId} in set ID: ${setId}`);

    // First verify the set exists
    const waterfallSet = await this.setRepository.findOne({
      where: { id: setId },
    });

    if (!waterfallSet) {
      throw new NotFoundException('Waterfall set not found');
    }

    // Find the question
    const question = await this.questionRepository.findOne({
      where: { id: questionId, setId },
    });

    if (!question) {
      throw new NotFoundException('Waterfall question not found');
    }

    try {
      // Update only the active status
      question.isActive = dto.is_active;
      await this.questionRepository.save(question);

      return {
        id: question.id,
        question_text: question.questionText,
        question_text_plain: question.questionTextPlain,
        correct_answers: question.correctAnswers,
        options: question.options,
        created_at: question.createdAt,
        updated_at: question.updatedAt,
        time_limit_in_seconds: question.timeLimitInSeconds,
        level: question.level,
        is_active: question.isActive,
      };
    } catch (error) {
      this.logger.error(`Failed to toggle waterfall question status with ID ${questionId}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not update waterfall question status. Please try again later.');
    }
  }

  /**
   * Get a single waterfall question by ID
   * @param questionId The ID of the question to retrieve
   * @returns The waterfall question
   */
  async getQuestionById(questionId: string): Promise<WaterfallQuestionResponseDto> {
    try {
      // Find the question
      const question = await this.questionRepository.findOne({
        where: { id: questionId },
        relations: ['set']
      });

      if (!question) {
        this.logger.warn(`Waterfall question with ID ${questionId} not found`);
        throw new NotFoundException(`The requested question was not found. Please check and try again.`);
      }

      // Map to response DTO
      return {
        id: question.id,
        question_text: question.questionText,
        question_text_plain: question.questionTextPlain,
        correct_answers: question.correctAnswers,
        options: question.options,
        created_at: question.createdAt,
        updated_at: question.updatedAt,
        time_limit_in_seconds: question.timeLimitInSeconds,
        level: question.level,
        is_active: question.isActive,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(`Error retrieving waterfall question with ID ${questionId}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve the question. Please try again later.');
    }
  }
}
