import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { Message } from './message.entity';

/**
 * Status of the conversation
 * @enum {string}
 */
export enum ConversationStatus {
  /** Conversation is active */
  ACTIVE = 'active',
  /** Conversation is archived */
  ARCHIVED = 'archived',
  /** Conversation is blocked */
  BLOCKED = 'blocked',
}

/**
 * Type of the conversation
 * @enum {string}
 */
export enum ConversationType {
  /** One-to-one conversation */
  DIRECT = 'direct',
  /** Group conversation (for future use) */
  GROUP = 'group',
}

@Entity()
export class Conversation extends AuditableBaseEntity {
  @Column({
    name: 'type',
    type: 'enum',
    enum: ConversationType,
    default: ConversationType.DIRECT,
  })
  type: ConversationType;

  @Column({
    name: 'status',
    type: 'enum',
    enum: ConversationStatus,
    default: ConversationStatus.ACTIVE,
  })
  status: ConversationStatus;

  @Column({ name: 'participant1_id' })
  participant1Id: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'participant1_id' })
  participant1: User;

  @Column({ name: 'participant2_id' })
  participant2Id: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'participant2_id' })
  participant2: User;

  @Column({ name: 'last_message_at', nullable: true })
  lastMessageAt: Date;

  @Column({ name: 'last_message_text', nullable: true, type: 'text' })
  lastMessageText: string;

  @Column({ name: 'last_message_sender_id', nullable: true })
  lastMessageSenderId: string;

  @OneToMany(() => Message, (message) => message.conversation)
  messages: Message[];

  @Column({ name: 'participant1_unread_count', default: 0 })
  participant1UnreadCount: number;

  @Column({ name: 'participant2_unread_count', default: 0 })
  participant2UnreadCount: number;
}
