import { Controller, Post, Get, Query, UseGuards, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { AwardScheduler } from './award.scheduler';
import { AdminGuard } from '../../common/guards/admin.guard';

@ApiTags('Award Scheduler')
@Controller('award-scheduler')
@UseGuards(AdminGuard)
@ApiBearerAuth('JWT-auth')
export class AwardSchedulerController {
  constructor(private readonly awardScheduler: AwardScheduler) {}

  /**
   * Get scheduler status and next run information
   */
  @Get('status')
  @ApiOperation({
    summary: 'Get award scheduler status (Admin only)',
    description: 'Returns the current status of the award scheduler including processing flags and schedule information. Requires admin authentication.',
  })
  @ApiResponse({
    status: 200,
    description: 'Scheduler status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        weeklyProcessing: { type: 'boolean', example: false },
        monthlyProcessing: { type: 'boolean', example: false },
        quarterlyProcessing: { type: 'boolean', example: false },
        annualProcessing: { type: 'boolean', example: false },
        nextWeeklyRun: { type: 'string', example: 'Every Sunday at 00:30 UTC (Diary module only)' },
        nextMonthlyRun: { type: 'string', example: 'Every 1st of month at 02:00 UTC (All modules)' },
        nextQuarterlyRun: { type: 'string', example: 'Every 1st of quarter at 01:00 UTC (All modules)' },
        nextAnnualRun: { type: 'string', example: 'January 1st at 03:00 UTC (All modules)' },
        lastWeeklyRun: { type: 'string', format: 'date-time', nullable: true },
        lastMonthlyRun: { type: 'string', format: 'date-time', nullable: true },
        lastQuarterlyRun: { type: 'string', format: 'date-time', nullable: true },
        lastAnnualRun: { type: 'string', format: 'date-time', nullable: true },
        lastWeeklyPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            weekStarting: { type: 'string', format: 'date' },
            weekEnding: { type: 'string', format: 'date' },
          },
        },
        lastMonthlyPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            month: { type: 'number' },
            year: { type: 'number' },
          },
        },
        lastQuarterlyPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            quarter: { type: 'number' },
            year: { type: 'number' },
          },
        },
        lastAnnualPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            year: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Admin access required' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  getSchedulerStatus() {
    // Return the status directly without extra wrapping
    return this.awardScheduler.getStatus();
  }

  /**
   * Diagnostic endpoint to check award system health
   */
  @Get('diagnostic')
  @ApiOperation({
    summary: 'Get award system diagnostic information (Admin only)',
    description: 'Returns diagnostic information about the award system including database status, award counts, and recent activity.',
  })
  async getAwardDiagnostic() {
    return await this.awardScheduler.getDiagnosticInfo();
  }

  /**
   * Manually trigger award generation for testing
   */
  @Post('trigger/test')
  @ApiOperation({
    summary: 'Manually trigger award generation for testing (Admin only)',
    description: 'Triggers award generation for the last completed period to test the system immediately.',
  })
  async triggerTestAwards() {
    try {
      await this.awardScheduler.triggerTestAwards();
      return {
        success: true,
        message: 'Test award generation triggered successfully',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to trigger test awards: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Manually trigger weekly award generation
   */
  @Post('trigger/weekly')
  @ApiOperation({
    summary: 'Manually trigger weekly award generation (Diary module only)',
    description:
      'Generates weekly awards for the diary module. If no parameters are provided, processes the previous week (Monday to Sunday). If parameters are provided, the day should be Sunday for week ending.',
  })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults to current year or calculated based on other params)' })
  @ApiQuery({ name: 'month', required: false, description: 'Month 1-12 (defaults to current month or calculated based on other params)' })
  @ApiQuery({ name: 'day', required: false, description: 'Day (should be Sunday for week ending, defaults to previous week if no params provided)' })
  @ApiResponse({ status: 200, description: 'Weekly awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerWeeklyAwards(
    @Query('year', new ParseIntPipe({ optional: true })) year?: number,
    @Query('month', new ParseIntPipe({ optional: true })) month?: number,
    @Query('day', new ParseIntPipe({ optional: true })) day?: number,
  ) {
    try {
      await this.awardScheduler.triggerWeeklyAwards(year, month, day);
      const targetDate = new Date(Date.UTC(year || new Date().getFullYear(), (month || new Date().getMonth() + 1) - 1, day || new Date().getDate()));
      return {
        message: `Weekly awards generated successfully for week ending ${targetDate.toISOString().slice(0, 10)}`,
        period: {
          year: year || new Date().getFullYear(),
          month: month || new Date().getMonth() + 1,
          day: day || new Date().getDate(),
          weekEnding: targetDate.toISOString().slice(0, 10),
        },
      };
    } catch (error) {
      throw new Error(`Failed to generate weekly awards: ${error.message}`);
    }
  }

  /**
   * Manually trigger quarterly award generation
   */
  @Post('trigger/quarterly')
  @ApiOperation({
    summary: 'Manually trigger quarterly award generation',
    description: 'Generates quarterly awards for all modules. If no parameters are provided, processes the previous quarter. Smart year adjustment handles cross-year quarters automatically.',
  })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults intelligently based on quarter and current date)' })
  @ApiQuery({ name: 'quarter', required: false, description: 'Quarter 1-4 (defaults to previous quarter)' })
  @ApiResponse({ status: 200, description: 'Quarterly awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerQuarterlyAwards(@Query('year', new ParseIntPipe({ optional: true })) year?: number, @Query('quarter', new ParseIntPipe({ optional: true })) quarter?: number) {
    await this.awardScheduler.triggerQuarterlyAwards(year, quarter);
    const currentQuarter = Math.floor(new Date().getMonth() / 3) + 1;
    const targetQuarter = quarter || (currentQuarter === 1 ? 4 : currentQuarter - 1);
    const targetYear = year || new Date().getFullYear();

    return {
      message: `Quarterly awards generated successfully for Q${targetQuarter}/${targetYear}`,
      period: {
        year: targetYear,
        quarter: targetQuarter,
      },
    };
  }

  /**
   * Manually trigger monthly award generation
   */
  @Post('trigger/monthly')
  @ApiOperation({
    summary: 'Manually trigger monthly award generation',
    description: 'Generates monthly awards for all modules. If no parameters are provided, processes the previous month. Smart year adjustment handles cross-year months automatically.',
  })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults intelligently based on month and current date)' })
  @ApiQuery({ name: 'month', required: false, description: 'Month 1-12 (defaults to previous month)' })
  @ApiResponse({ status: 200, description: 'Monthly awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerMonthlyAwards(@Query('year', new ParseIntPipe({ optional: true })) year?: number, @Query('month', new ParseIntPipe({ optional: true })) month?: number) {
    try {
      await this.awardScheduler.triggerMonthlyAwards(year, month);
      return {
        message: `Monthly awards generated successfully for ${month || 'previous month'}/${year || 'current year'}`,
        period: {
          year: year || new Date().getFullYear(),
          month: month || (new Date().getMonth() === 0 ? 12 : new Date().getMonth()),
        },
      };
    } catch (error) {
      throw new Error(`Failed to generate monthly awards: ${error.message}`);
    }
  }

  /**
   * Manually trigger annual award generation
   */
  @Post('trigger/annual')
  @ApiOperation({ summary: 'Manually trigger annual award generation' })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults to previous year)' })
  @ApiResponse({ status: 200, description: 'Annual awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerAnnualAwards(@Query('year', new ParseIntPipe({ optional: true })) year?: number) {
    await this.awardScheduler.triggerAnnualAwards(year);
    return {
      message: `Annual awards generated successfully for ${year || 'previous year'}`,
      period: {
        year: year || new Date().getFullYear() - 1,
      },
    };
  }
}
