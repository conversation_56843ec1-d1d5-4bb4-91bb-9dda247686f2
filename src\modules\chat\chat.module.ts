import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MulterModule } from '@nestjs/platform-express';
import { ChatService } from './chat.service';
import { ChatGateway } from './chat.gateway';
import { <PERSON>t<PERSON>ontroller } from './chat.controller';
import { AdminChatController } from './admin-chat.controller';
import { AdminChatService } from './admin-chat.service';
import { Conversation } from '../../database/entities/conversation.entity';
import { Message } from '../../database/entities/message.entity';
import { MessageAttachment } from '../../database/entities/message-attachment.entity';
import { MessageRegistry } from '../../database/entities/message-registry.entity';
import { User } from '../../database/entities/user.entity';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';
import { StudentFriendship } from '../../database/entities/student-friendship.entity';
import { NotificationModule } from '../notification/notification.module';
import { CommonModule } from '../../common/common.module';
import { DeeplinkModule } from '../../common/utils/deeplink.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Conversation, Message, MessageAttachment, MessageRegistry, User, StudentTutorMapping, StudentFriendship]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '1d' },
      }),
    }),
    NotificationModule,
    CommonModule,
    DeeplinkModule,
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  ],
  controllers: [ChatController, AdminChatController],
  providers: [ChatService, ChatGateway, AdminChatService],
  exports: [ChatService, AdminChatService],
})
export class ChatModule {}
