import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Patch,
  Delete,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Req,
  Query,
  BadRequestException,
  ValidationPipe,
  Res,
  NotFoundException,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiTags, ApiQuery, ApiConsumes } from '@nestjs/swagger';
import { DiaryService } from './diary.service';
import { DiaryLikeService } from './diary-like.service';
import { DiaryFriendShareService } from './diary-friend-share.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';
import { RequireFeature } from '../../common/decorators/require-feature.decorator';
import { FeatureType } from '../../database/entities/plan-feature.entity';
import { StudentOnly } from '../../common/decorators/student-only.decorator';
import { Public } from '../../common/decorators/public-api.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  CreateDiaryEntryDto,
  UpdateDiaryEntryDto,
  ShareDiaryEntryDto,
  DiaryEntryResponseDto,
  DiarySkinResponseDto,
  DiaryShareResponseDto,
  StudentTutorListResponseDto,
  DiaryAwardsResponseDto,
  DiaryPeriodAwardsResponseDto,
  DiaryEntryFilterDto,
  CreateStudentDiarySkinDto,
  UpdateStudentDiarySkinDto,
  UpdateTutorGreetingDto,
  SubmitDiaryEntryDto,
  UpdateDefaultSkinDto,
  UpdateTodaysDiarySkinDto,
  ToggleSkinStatusDto,
  UpdateDiaryEntrySettingsDto,
  DiaryCoverPhotoResponseDto,
  DiaryEntryHistoryResponseDto,
  DiaryEntryVersionDto,
} from '../../database/models/diary.dto';
import { ShareDiaryEntryWithFriendDto, DiaryFriendShareResponseDto } from '../../database/models/diary-friend-share.dto';
import { SetDefaultSkinDto } from '../../database/models/users.dto';
import { AddThanksMessageDto } from '../../database/models/diary-thanks.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../common/decorators/api-response.decorator';
import { ApiOkResponse } from '@nestjs/swagger';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { DiaryLikeResponseDto, DiaryLikeCountResponseDto } from '../../database/models/diary-like.dto';
import { LikerType } from '../../database/entities/diary-entry-like.entity';
import { StudentTutorGuard } from '../../common/guards/student-tutor.guard';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { CurrentUserService } from '../../common/services/current-user.service';
import * as path from 'path';
import * as fs from 'fs';
import { Response } from 'express';

@ApiTags('diary')
@Controller('diary')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class DiaryController {
  constructor(
    private readonly diaryService: DiaryService,
    private readonly diaryLikeService: DiaryLikeService,
    private readonly fileRegistryService: FileRegistryService,
    private readonly currentUserService: CurrentUserService,
    private readonly diaryFriendShareService: DiaryFriendShareService,
  ) {}

  @Get('skins')
  @ApiOperation({
    summary: 'Get all diary skins',
    description: 'Get a list of all available diary skins/templates.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(DiarySkinResponseDto, 'List of diary skins retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiarySkins(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiarySkinResponseDto>>> {
    const studentId = req.user.id;
    const skins = await this.diaryService.getDiarySkins(false, studentId, paginationDto); // Include student's own skins
    return ApiResponse.success(skins, 'Diary skins retrieved successfully');
  }
  @Post('skins')
  @UseInterceptors(FileInterceptor('previewImage'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new student diary skin',
    description: 'Creates a new diary skin/template for the student. Only accessible by the student who owns it.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'My Custom Skin',
          description: 'Name of the diary skin',
        },
        description: {
          type: 'string',
          example: 'My personal theme with custom colors',
          description: 'Description of the diary skin',
        },
        templateContent: {
          type: 'string',
          example: '<div class="student-diary-template">{{content}}</div>',
          description: 'HTML template content for the skin',
        },
        isActive: {
          type: 'boolean',
          example: true,
          description: 'Whether the skin is active and available for use',
        },
        previewImage: {
          type: 'string',
          format: 'binary',
          description: 'Preview image file for the diary skin (JPEG, PNG, or GIF)',
        },
      },
      required: ['name', 'description', 'templateContent'],
    },
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Student diary skin created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(409, 'Diary skin with the same name already exists')
  async createStudentDiarySkin(@Req() req: any, @Body() createStudentDiarySkinDto: CreateStudentDiarySkinDto, @UploadedFile() previewImage?: any): Promise<ApiResponse<DiarySkinResponseDto>> {
    if (!previewImage) {
      throw new BadRequestException('Preview image is required');
    }

    const studentId = req.user.id;
    const skin = await this.diaryService.createStudentDiarySkin(studentId, createStudentDiarySkinDto, previewImage);
    return ApiResponse.success(skin, 'Student diary skin created successfully', 201);
  }

  @Get('skins/:id')
  @ApiOperation({
    summary: 'Get a diary skin by ID',
    description: 'Get details of a specific diary skin by ID. For student skins, only the owner can access it.',
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Diary skin retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  async getDiarySkinById(@Param('id') id: string, @Req() req: any): Promise<ApiResponse<DiarySkinResponseDto>> {
    const studentId = req.user.id;

    try {
      // First try to get it as a student skin
      const skin = await this.diaryService.getStudentDiarySkinById(id, studentId);
      return ApiResponse.success(skin, 'Student diary skin retrieved successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        // If not found as a student skin, try to get it as a global skin
        try {
          const skin = await this.diaryService.getDiarySkinById(id);
          return ApiResponse.success(skin, 'Diary skin retrieved successfully');
        } catch (innerError) {
          throw innerError;
        }
      }
      throw error;
    }
  }

  @Patch('skins/:id')
  @UseInterceptors(FileInterceptor('previewImage'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update a student diary skin',
    description: 'Update an existing student diary skin. Only accessible by the student who owns it.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'My Updated Custom Skin',
          description: 'Name of the diary skin',
        },
        description: {
          type: 'string',
          example: 'My updated personal theme with custom colors',
          description: 'Description of the diary skin',
        },
        templateContent: {
          type: 'string',
          example: '<div class="updated-student-diary-template">{{content}}</div>',
          description: 'HTML template content for the skin',
        },
        isActive: {
          type: 'boolean',
          example: false,
          description: 'Whether the skin is active and available for use',
        },
        previewImage: {
          type: 'string',
          format: 'binary',
          description: 'Preview image file for the diary skin (JPEG, PNG, or GIF)',
        },
      },
    },
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Student diary skin updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Student diary skin not found')
  @ApiErrorResponse(409, 'Diary skin with the same name already exists')
  async updateStudentDiarySkin(
    @Param('id') id: string,
    @Req() req: any,
    @Body() updateStudentDiarySkinDto: UpdateStudentDiarySkinDto,
    @UploadedFile() previewImage?: any,
  ): Promise<ApiResponse<DiarySkinResponseDto>> {
    const studentId = req.user.id;
    const skin = await this.diaryService.updateStudentDiarySkin(id, studentId, updateStudentDiarySkinDto, previewImage);
    return ApiResponse.success(skin, 'Student diary skin updated successfully');
  }

  @Delete('skins/:id')
  @ApiOperation({
    summary: 'Delete a student diary skin',
    description: 'Delete a student diary skin. Only accessible by the student who owns it.',
  })
  @ApiOkResponseWithType(Object, 'Student diary skin deleted successfully')
  @ApiErrorResponse(400, 'Cannot delete skin as it is being used by diary entries')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Student diary skin not found')
  async deleteStudentDiarySkin(@Param('id') id: string, @Req() req: any): Promise<ApiResponse<null>> {
    const studentId = req.user.id;
    await this.diaryService.deleteStudentDiarySkin(id, studentId);
    return ApiResponse.success(null, 'Student diary skin deleted successfully');
  }

  @Patch('skins/:id/status')
  @ApiOperation({
    summary: 'Toggle student diary skin active status',
    description: 'Activate or deactivate a student diary skin. Only accessible by the student who owns it.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the skin to toggle',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: ToggleSkinStatusDto,
    description: 'Status toggle data',
    examples: {
      activate: {
        summary: 'Activate skin',
        description: 'Set skin as active',
        value: {
          isActive: true,
        },
      },
      deactivate: {
        summary: 'Deactivate skin',
        description: 'Set skin as inactive',
        value: {
          isActive: false,
        },
      },
    },
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Skin status updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Skin not found or does not belong to student')
  @ApiErrorResponse(500, 'Internal server error')
  async toggleStudentSkinStatus(
    @Param('id') skinId: string,
    @Req() req: any,
    @Body() toggleSkinStatusDto: ToggleSkinStatusDto,
  ): Promise<ApiResponse<DiarySkinResponseDto>> {
    const studentId = req.user.id;
    const result = await this.diaryService.toggleStudentSkinStatus(skinId, studentId, toggleSkinStatusDto);
    return ApiResponse.success(result, `Skin ${toggleSkinStatusDto.isActive ? 'activated' : 'deactivated'} successfully`);
  }

  @Patch('skins/:id/set-as-default')
  @ApiOperation({
    summary: 'Set diary skin as default for student',
    description: 'Set a specific diary skin as the default for diary entries. The skin must be accessible to the student (free, purchased, or owned).',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary skin to set as default',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(Object, 'Default diary skin updated successfully')
  @ApiErrorResponse(400, 'Invalid skin ID')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - You do not have access to this skin')
  @ApiErrorResponse(404, 'Diary skin not found')
  async setDefaultDiarySkin(@Param('id') skinId: string, @Req() req: any): Promise<ApiResponse<void>> {
    const studentId = req.user.id;
    await this.diaryService.setDefaultDiarySkin(studentId, skinId);
    return ApiResponse.success(null, 'Default diary skin updated successfully');
  }

  @Get('skins/my-default')
  @ApiOperation({
    summary: 'Get student default diary skin',
    description: 'Get the current default diary skin for the authenticated student.',
  })
  @ApiOkResponseWithType(DiarySkinResponseDto, 'Default diary skin retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(404, 'Default diary skin not found')
  async getDefaultDiarySkin(@Req() req: any): Promise<ApiResponse<DiarySkinResponseDto | null>> {
    const studentId = req.user.id;
    const defaultSkin = await this.diaryService.getDefaultDiarySkin(studentId);
    return ApiResponse.success(defaultSkin, 'Default diary skin retrieved successfully');
  }

  // Diary-specific operations (student only)
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @Post('entries')
  @ApiOperation({
    summary: 'Create a diary entry for a specific date',
    description:
      "Create a diary entry for a specific date (past or future). Use this endpoint when you need to create entries for dates other than today. For today's entry, use the GET /diary/entries/today endpoint instead. If an entry already exists for the specified date, returns the existing entry.",
  })
  @ApiBody({
    type: CreateDiaryEntryDto,
    description: 'Diary entry initialization data (entryDate is required)',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry created or retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Forbidden - Only students can create diary entries')
  @ApiErrorResponse(500, 'Internal server error')
  async createDiaryEntry(
    @Req() req: any,
    @Body(
      new ValidationPipe({
        whitelist: false,
        forbidNonWhitelisted: false,
        transform: true,
      }),
    )
    createDiaryEntryDto: CreateDiaryEntryDto,
  ): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;

    // Ensure entryDate is provided
    if (!createDiaryEntryDto.entryDate) {
      throw new BadRequestException('entryDate is required for creating diary entries for specific dates');
    }

    const entry = await this.diaryService.createDiaryEntry(userId, createDiaryEntryDto);

    // Determine if this was a new entry or an existing one    // Determine status code based on whether the entry was just created or already existed
    const statusCode = entry.createdAt.toISOString() === entry.updatedAt.toISOString() ? 201 : 200;
    const message = statusCode === 201 ? `Diary entry for ${createDiaryEntryDto.entryDate} created successfully` : `Existing diary entry for ${createDiaryEntryDto.entryDate} retrieved successfully`;

    return ApiResponse.success(entry, message, statusCode);
  } // Diary-specific operations (student only)
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @Get('entries/search')
  @ApiOperation({
    summary: 'Search diary entries by title or date',
    description: 'Search through diary entries by title/subject or specific date',
  })
  @ApiQuery({
    name: 'date',
    required: false,
    type: String,
    description: 'Date to filter entries (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'subject',
    required: false,
    type: String,
    description: 'Title/subject to search for',
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'List of diary entries that match the search criteria')
  @ApiErrorResponse(400, 'Invalid search parameters')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Access forbidden')
  async searchDiaryEntries(
    @Req() req: any,
    @Query(
      new ValidationPipe({
        transform: true,
        transformOptions: { enableImplicitConversion: true },
        whitelist: true,
      }),
    )
    filterDto: DiaryEntryFilterDto,
  ): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    const entries = await this.diaryService.searchDiaryEntries(req.user.id, filterDto, undefined);
    return ApiResponse.success(entries, 'Diary entries retrieved successfully');
  }

  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @Get('entries')
  @ApiOperation({
    summary: 'Get all diary entries for the current user',
    description: 'Retrieve all diary entries created by the current user.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Diary entries retrieved successfully')
  @ApiErrorResponse(400, 'Unable to retrieve diary entries')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntries(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    try {
      const userId = req.user.sub;
      // Always pass false for includeFriends to ensure only own entries are returned
      const entries = await this.diaryService.getStudentDiaryEntries(userId, paginationDto, false);
      return ApiResponse.success(entries, 'Diary entries retrieved successfully');
    } catch (error) {
      // Let NestJS handle the exception and transform it into the appropriate HTTP response
      throw error;
    }
  }

  @Get('friends/entries')
  @ApiOperation({
    summary: 'Get diary entries from friends',
    description: 'Retrieve diary entries from friends that have been shared with the current user.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Friend diary entries retrieved successfully')
  @ApiErrorResponse(400, 'Unable to retrieve friend diary entries')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getFriendDiaryEntries(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    try {
      const userId = req.user.sub;
      const entries = await this.diaryService.getStudentDiaryEntries(userId, paginationDto, true);
      return ApiResponse.success(entries, 'Friend diary entries retrieved successfully');
    } catch (error) {
      // Let NestJS handle the exception and transform it into the appropriate HTTP response
      throw error;
    }
  }

  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @Get('entries/today')
  @ApiOperation({
    summary: "Get or create today's diary entry",
    description: 'Get the diary entry for today for the currently authenticated student. If no entry exists, a new one will be created with minimal initialization.',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, "Today's diary entry retrieved or created successfully")
  @ApiErrorResponse(400, "Unable to retrieve or create today's diary entry")
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getTodaysDiaryEntry(@Req() req: any): Promise<ApiResponse<DiaryEntryResponseDto>> {
    try {
      const userId = req.user.sub;
      const entry = await this.diaryService.getTodaysDiaryEntry(userId);

      // Determine if this was a new entry or an existing one based on creation time
      const isNewEntry = entry.createdAt && new Date(entry.createdAt).getTime() > Date.now() - 5000;
      const message = isNewEntry ? "Today's diary entry created successfully" : "Today's diary entry retrieved successfully";

      return ApiResponse.success(entry, message);
    } catch (error) {
      // Let NestJS handle the exception and transform it into the appropriate HTTP response
      throw error;
    }
  }

  @Get('entries/:id')
  @ApiOperation({
    summary: 'Get a specific diary entry',
    description: 'Retrieve a specific diary entry by its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to retrieve',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to view this diary entry')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntry(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const entry = await this.diaryService.getDiaryEntry(id, userId);
    return ApiResponse.success(entry, 'Diary entry retrieved successfully');
  }

  @Patch('entries/:id')
  @ApiOperation({
    summary: 'Update a diary entry',
    description: 'Update an existing diary entry.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to update',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: UpdateDiaryEntryDto,
    description: 'Diary entry update data',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to update this diary entry or only students can update diary entries')
  @ApiErrorResponse(500, 'Internal server error')
  async updateDiaryEntry(
    @Req() req: any,
    @Param('id') id: string,
    @Body(
      new ValidationPipe({
        whitelist: false,
        forbidNonWhitelisted: false,
        transform: true,
      }),
    )
    updateDiaryEntryDto: UpdateDiaryEntryDto,
  ): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    // Pass request object for implicit metadata capture
    const entry = await this.diaryService.updateDiaryEntry(id, userId, updateDiaryEntryDto, req);
    return ApiResponse.success(entry, 'Diary entry updated successfully');
  }

  @Patch('entries/:id/settings')
  @ApiOperation({
    summary: 'Update diary entry settings',
    description: 'Update the settings template for an existing diary entry. This will change the word limits and level for the entry.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to update settings for',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: UpdateDiaryEntrySettingsDto,
    description: 'New settings template ID',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry settings updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found or settings template not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to update this diary entry or only students can update diary entries')
  @ApiErrorResponse(500, 'Internal server error')
  async updateDiaryEntrySettings(
    @Req() req: any,
    @Param('id') id: string,
    @Body(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    )
    updateSettingsDto: UpdateDiaryEntrySettingsDto,
  ): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const entry = await this.diaryService.updateDiaryEntrySettings(id, userId, updateSettingsDto);
    return ApiResponse.success(entry, 'Diary entry settings updated successfully');
  }

  @Post('entries/:id/submit')
  @ApiOperation({
    summary: 'Submit a diary entry for review',
    description: 'Submit a diary entry for review by a tutor. Requires complete entry data including title, content, and settings.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to submit',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: SubmitDiaryEntryDto,
    description: 'Complete diary entry data for submission',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry submitted successfully')
  @ApiErrorResponse(400, 'Invalid input or missing required fields')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to submit this diary entry')
  @ApiErrorResponse(409, 'Conflict - This diary entry is not in the correct state for submission')
  @ApiErrorResponse(500, 'Internal server error')
  async submitDiaryEntry(@Req() req: any, @Param('id') id: string, @Body() submitDiaryEntryDto: SubmitDiaryEntryDto): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const entry = await this.diaryService.submitDiaryEntry(id, userId, submitDiaryEntryDto);
    return ApiResponse.success(entry, 'Diary entry submitted successfully');
  }

  @Post('entries/:id/share')
  @ApiOperation({
    summary: 'Share a diary entry',
    description: 'Share a diary entry and generate a QR code. Makes the entry public.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to share',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: ShareDiaryEntryDto,
    description: 'Diary entry sharing data',
  })
  @ApiOkResponseWithType(DiaryShareResponseDto, 'Diary entry shared successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to share this diary entry')
  @ApiErrorResponse(500, 'Internal server error')
  async shareDiaryEntry(@Req() req: any, @Param('id') id: string, @Body() shareDiaryEntryDto: ShareDiaryEntryDto): Promise<ApiResponse<DiaryShareResponseDto>> {
    const userId = req.user.sub;
    const result = await this.diaryService.shareDiaryEntry(id, userId, shareDiaryEntryDto);
    return ApiResponse.success(result, 'Diary entry shared successfully');
  }

  @UseGuards(StudentGuard)
  @Post('entries/:id/share-with-friend')
  @ApiOperation({
    summary: 'Share a diary entry with a friend',
    description: 'Share a diary entry with a specific friend who has diary viewing permission. Sends a chat message with the shared entry link and generates a deep link for access.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to share',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: ShareDiaryEntryWithFriendDto,
    description: 'Friend sharing data with target user ID',
  })
  @ApiOkResponseWithType(DiaryFriendShareResponseDto, 'Diary entry shared with friend successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - You can only share with friends who have diary viewing permission')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(409, 'Conflict - This diary entry has already been shared with this friend')
  @ApiErrorResponse(500, 'Internal server error')
  async shareDiaryEntryWithFriend(@Req() req: any, @Param('id') entryId: string, @Body() shareDto: ShareDiaryEntryWithFriendDto): Promise<ApiResponse<DiaryFriendShareResponseDto>> {
    const userId = req.user.sub;
    const result = await this.diaryFriendShareService.shareDiaryEntryWithFriend(entryId, userId, shareDto);
    return ApiResponse.success(result, 'Diary entry shared with friend successfully');
  }

  @Public()
  @Get('shared-entries')
  @ApiOperation({
    summary: 'Get all publicly shared diary entries',
    description: 'Retrieve all diary entries that have been publicly shared by any user.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Publicly shared diary entries retrieved successfully')
  @ApiErrorResponse(400, 'Unable to retrieve shared diary entries')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllSharedDiaryEntries(@Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    // Extract user ID if user is authenticated (for hasLiked calculation)
    // Since this is a @Public() endpoint, use CurrentUserService which is populated by CurrentUserMiddleware for all routes
    const userId = this.currentUserService.getCurrentUserId();
    const entries = await this.diaryService.getPubliclySharedEntries(paginationDto, userId);
    return ApiResponse.success(entries, 'Publicly shared diary entries retrieved successfully');
  }

  @Post('entries/:id/make-private')
  @ApiOperation({
    summary: 'Make a diary entry private',
    description: 'Make a diary entry private and deactivate all share links.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to make private',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(Object, 'Diary entry made private successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to modify this diary entry')
  @ApiErrorResponse(500, 'Internal server error')
  async makeEntryPrivate(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<any>> {
    const userId = req.user.sub;
    await this.diaryService.makeEntryPrivate(id, userId);
    return ApiResponse.success({ success: true }, 'Diary entry made private successfully');
  }

  @Get('tutors')
  @ApiOperation({
    summary: 'Get all tutors available for sharing',
    description: 'Get a list of all tutors that the student can share diary entries with.',
  })
  @ApiOkResponseWithType(StudentTutorListResponseDto, 'Tutors retrieved successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentTutors(@Req() req: any): Promise<ApiResponse<StudentTutorListResponseDto>> {
    const userId = req.user.sub;
    const tutors = await this.diaryService.getStudentTutors(userId);
    return ApiResponse.success(tutors, 'Tutors retrieved successfully');
  }

  @Get('awards')
  @ApiOperation({
    summary: 'Get student awards',
    description: 'Get all awards earned by the current student.',
  })
  @ApiOkResponseWithType(DiaryAwardsResponseDto, 'Awards retrieved successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentAwards(@Req() req: any): Promise<ApiResponse<DiaryAwardsResponseDto>> {
    const userId = req.user.sub;
    const awards = await this.diaryService.getStudentAwards(userId);
    return ApiResponse.success(awards, 'Awards retrieved successfully');
  }

  @Get('period-awards')
  @ApiOperation({
    summary: 'Get student period awards',
    description: 'Get all period awards (weekly/monthly) earned by the current student.',
  })
  @ApiOkResponseWithType(DiaryPeriodAwardsResponseDto, 'Period awards retrieved successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentPeriodAwards(@Req() req: any): Promise<ApiResponse<DiaryPeriodAwardsResponseDto>> {
    const userId = req.user.sub;
    const awards = await this.diaryService.getStudentPeriodAwards(userId);
    return ApiResponse.success(awards, 'Period awards retrieved successfully');
  }

  @Post('greeting')
  @ApiOperation({
    summary: 'Set tutor greeting message',
    description: 'Set a greeting message for tutors. This is required before creating diary entries.',
  })
  @ApiBody({
    type: UpdateTutorGreetingDto,
    description: 'Greeting message for tutors',
  })
  @ApiOkResponseWithType(Object, 'Greeting message set successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async setTutorGreeting(@Req() req: any, @Body() updateTutorGreetingDto: UpdateTutorGreetingDto): Promise<ApiResponse<any>> {
    const userId = req.user.sub;
    await this.diaryService.updateTutorGreeting(userId, updateTutorGreetingDto.greeting);
    return ApiResponse.success({ success: true }, 'Greeting message set successfully');
  }

  @Post('entries/:id/thanks-message')
  @ApiOperation({
    summary: 'Add thanks message to a diary entry',
    description: 'Add a thanks message to a diary entry after it has been reviewed and confirmed by a tutor.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: AddThanksMessageDto,
    description: 'Thanks message data',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Thanks message added successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to add a thanks message to this diary entry')
  @ApiErrorResponse(409, 'Conflict - This diary entry is not in the correct state for adding a thanks message')
  @ApiErrorResponse(500, 'Internal server error')
  async addThanksMessage(@Req() req: any, @Param('id') id: string, @Body() addThanksMessageDto: AddThanksMessageDto): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    // Add the thanks message
    await this.diaryService.addThanksMessage(id, userId, addThanksMessageDto.thanksMessage);

    // Get the complete entry with all relations for the response
    const completeEntry = await this.diaryService.getDiaryEntry(id, userId);
    return ApiResponse.success(completeEntry, 'Thanks message added successfully');
  }

  // Version History Endpoints
  @Get('entries/:id/history')
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @ApiOperation({
    summary: 'Get diary entry version history',
    description: 'View all previous versions of your diary entry. Shows the complete history of changes made to the entry.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(DiaryEntryHistoryResponseDto, 'Version history retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to view this diary entry history')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntryHistory(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryEntryHistoryResponseDto>> {
    const userId = req.user.sub;
    const history = await this.diaryService.getDiaryEntryHistory(id, userId);
    return ApiResponse.success(history, 'Version history retrieved successfully');
  }

  @Get('entries/:id/versions/:versionId')
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @ApiOperation({
    summary: 'View a specific version',
    description: 'View the content of a specific version of your diary entry.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'versionId',
    description: 'The ID of the specific version',
    example: '456e7890-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(DiaryEntryVersionDto, 'Version retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Version not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to view this version')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntryVersion(@Req() req: any, @Param('id') id: string, @Param('versionId') versionId: string): Promise<ApiResponse<DiaryEntryVersionDto>> {
    const userId = req.user.sub;
    const version = await this.diaryService.getDiaryEntryVersion(versionId, userId);
    return ApiResponse.success(version, 'Version retrieved successfully');
  }

  @Put('entries/:id/versions/:versionId/restore')
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @ApiOperation({
    summary: 'Restore a previous version',
    description: 'Make a previous version the current/latest version of your diary entry. The restored content will still be validated against current settings.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'versionId',
    description: 'The ID of the version to restore',
    example: '456e7890-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Version restored successfully')
  @ApiErrorResponse(400, 'Invalid input or restored content violates current settings')
  @ApiErrorResponse(404, 'Diary entry or version not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to restore this version')
  @ApiErrorResponse(500, 'Internal server error')
  async restoreDiaryEntryVersion(@Req() req: any, @Param('id') id: string, @Param('versionId') versionId: string): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const entry = await this.diaryService.restoreDiaryEntryVersion(id, versionId, userId);
    return ApiResponse.success(entry, 'Version restored successfully');
  }

  // Admin/Debug Endpoints
  @Post('admin/fix-original-version/:id')
  @ApiOperation({
    summary: 'Fix missing original reviewed version for a specific entry',
    description: 'Creates an original reviewed version for entries that were corrected before the new system was implemented',
  })
  @ApiParam({ name: 'id', description: 'Diary entry ID' })
  @ApiOkResponse({ description: 'Original version fixed successfully' })
  async fixOriginalVersion(@Param('id') id: string): Promise<ApiResponse<any>> {
    const result = await this.diaryService.fixMissingOriginalVersionForEntry(id);
    return ApiResponse.success(result, 'Original version fixed successfully');
  }

  @Get('filter')
  @ApiOperation({
    summary: 'Filter diary entries',
    description: 'Filter diary entries by date or subject.',
  })
  @ApiQuery({
    name: 'date',
    required: false,
    description: 'Filter by date (YYYY-MM-DD)',
    example: '2023-07-25',
  })
  @ApiQuery({
    name: 'subject',
    required: false,
    description: 'Filter by subject',
    example: 'Mathematics',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Diary entries filtered successfully')
  @ApiErrorResponse(400, 'Invalid filter parameters')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async filterDiaryEntries(
    @Req() req: any,
    @Query('date') date?: string,
    @Query('subject') subject?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    const userId = req.user.sub;
    const filterDto = { date, subject };
    const paginationDto = { page, limit, sortBy, sortDirection };

    const entries = await this.diaryService.filterDiaryEntries(userId, filterDto, paginationDto);
    return ApiResponse.success(entries, 'Diary entries filtered successfully');
  }

  @Patch('default-skin')
  @ApiOperation({
    summary: 'Update default diary skin',
    description: 'Update the default diary skin for the currently authenticated student.',
  })
  @ApiBody({
    type: UpdateDefaultSkinDto,
    description: 'Default skin update data',
  })
  @ApiOkResponseWithType(Object, 'Default skin updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  @ApiErrorResponse(500, 'Internal server error')
  async updateDefaultSkin(@Req() req: any, @Body() updateDefaultSkinDto: UpdateDefaultSkinDto): Promise<ApiResponse<any>> {
    const userId = req.user.sub;
    await this.diaryService.setDefaultDiarySkin(userId, updateDefaultSkinDto.skinId);
    return ApiResponse.success({ success: true }, 'Default skin updated successfully');
  }

  @Patch('today/skin')
  @ApiOperation({
    summary: "Update today's diary entry skin",
    description: "Update the skin of today's diary entry for the currently authenticated student.",
  })
  @ApiBody({
    type: UpdateTodaysDiarySkinDto,
    description: "Today's diary skin update data",
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, "Today's diary skin updated successfully")
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Diary skin not found or no diary entry for today')
  @ApiErrorResponse(500, 'Internal server error')
  async updateTodaysDiarySkin(@Req() req: any, @Body() updateTodaysDiarySkinDto: UpdateTodaysDiarySkinDto): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const userId = req.user.sub;
    const updatedEntry = await this.diaryService.updateTodaysDiarySkin(userId, updateTodaysDiarySkinDto.skinId);
    return ApiResponse.success(updatedEntry, "Today's diary skin updated successfully");
  }

  @Post('entries/:id/like')
  @UseGuards(StudentTutorGuard)
  @ApiOperation({
    summary: 'Like a diary entry',
    description: 'Adds a like to the specified diary entry (only available to students and tutors)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary entry to like',
    type: 'string',
  })
  @ApiOkResponseWithType(DiaryLikeResponseDto, 'Diary entry liked successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can like diary entries')
  @ApiErrorResponse(404, 'Diary entry not found')
  async likeDiaryEntry(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryLikeResponseDto>> {
    const like = await this.diaryLikeService.addLike(id, req.user.id, req.user.type);
    return ApiResponse.success(like, 'Diary entry liked successfully');
  }

  @Delete('entries/:id/like')
  @UseGuards(StudentTutorGuard)
  @ApiOperation({
    summary: 'Unlike a diary entry',
    description: 'Removes a like from the specified diary entry (only available to students and tutors)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary entry to unlike',
    type: 'string',
  })
  @ApiOkResponseWithType(DiaryLikeResponseDto, 'Diary entry unliked successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can unlike diary entries')
  @ApiErrorResponse(404, 'Diary entry not found')
  async unlikeDiaryEntry(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryLikeResponseDto>> {
    await this.diaryLikeService.removeLike(id, req.user.id);
    return ApiResponse.success(null, 'Diary entry unliked successfully');
  }

  @Get('entries/:id/likes')
  @UseGuards(StudentTutorGuard)
  @ApiOperation({
    summary: 'Get diary entry likes',
    description: 'Get the number of likes and like status for the specified diary entry',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary entry',
    type: 'string',
  })
  @ApiOkResponseWithType(DiaryLikeCountResponseDto, 'Diary entry likes retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Only students and tutors can view like details')
  @ApiErrorResponse(404, 'Diary entry not found')
  async getDiaryEntryLikes(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryLikeCountResponseDto>> {
    const [likeCount, hasLiked] = await Promise.all([this.diaryLikeService.getLikeCount(id), this.diaryLikeService.hasUserLiked(id, req.user.id)]);
    return ApiResponse.success({ count: likeCount, hasLiked }, 'Diary entry likes retrieved successfully');
  }

  @Get('entries/:id/like-count')
  @ApiOperation({
    summary: 'Get diary entry like count',
    description: 'Get the number of likes for the specified diary entry',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary entry',
    type: 'string',
  })
  @ApiOkResponseWithType(DiaryLikeCountResponseDto, 'Diary entry like count retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @Public() // Make this endpoint public
  async getDiaryEntryLikeCount(@Param('id') id: string): Promise<ApiResponse<DiaryLikeCountResponseDto>> {
    const count = await this.diaryLikeService.getLikeCount(id);
    return ApiResponse.success({ count, hasLiked: false }, 'Diary entry like count retrieved successfully');
  }

  @Get('entries/:id/qr')
  @ApiOperation({
    summary: 'Download QR code for a shared diary entry',
    description: 'Download the QR code image that was generated when the diary entry was shared.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(Object, 'QR code downloaded successfully')
  @ApiErrorResponse(404, 'QR code not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to access this QR code')
  @ApiErrorResponse(500, 'Internal server error')
  async downloadQrCode(@Param('id') id: string, @Req() req: any, @Res() res: Response): Promise<void> {
    const userId = req.user.id;

    // Get the QR code file from registry
    const qrFile = await this.fileRegistryService.getFile(FileEntityType.DIARY_QR, id);

    if (!qrFile) {
      throw new NotFoundException('QR code not found. The diary entry may not be shared yet.');
    }

    // Read file and send it as response
    const filePath = path.resolve('uploads', qrFile.filePath);
    if (!fs.existsSync(filePath)) {
      throw new NotFoundException('QR code file not found');
    }

    res.setHeader('Content-Type', qrFile.mimeType || 'image/png');
    res.setHeader('Content-Disposition', `attachment; filename=${path.basename(qrFile.filePath)}`);
    fs.createReadStream(filePath).pipe(res);
  }

  // Diary Cover Photo Endpoints
  @Post('cover-photo')
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @UseInterceptors(FileInterceptor('coverPhoto'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload diary cover photo',
    description: "Upload a cover photo for the student's diary. Only accessible by students.",
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        coverPhoto: {
          type: 'string',
          format: 'binary',
          description: 'Cover photo file (JPEG, PNG, GIF, or WebP)',
        },
      },
      required: ['coverPhoto'],
    },
  })
  @ApiOkResponseWithType(DiaryCoverPhotoResponseDto, 'Diary cover photo uploaded successfully')
  @ApiErrorResponse(400, 'Invalid file or file too large')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(500, 'Internal server error')
  async uploadDiaryCoverPhoto(@Req() req: any, @UploadedFile() coverPhoto?: any): Promise<ApiResponse<DiaryCoverPhotoResponseDto>> {
    if (!coverPhoto) {
      throw new BadRequestException('Cover photo is required');
    }

    const userId = req.user.id;

    try {
      // Get or create the user's diary
      const diary = await this.diaryService.getOrCreateDiary(userId);

      // Upload the cover photo
      const uploadResult = await this.fileRegistryService.uploadFile(FileEntityType.DIARY_COVER, coverPhoto, diary.id, {
        userId: userId,
        entityId: diary.id,
      });

      // Get the file URL
      const fileUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_COVER, diary.id);

      // Create response DTO
      const responseDto: DiaryCoverPhotoResponseDto = {
        id: uploadResult.registry.id,
        diaryId: diary.id,
        userId: userId,
        fileName: uploadResult.registry.fileName,
        mimeType: uploadResult.registry.mimeType,
        fileSize: uploadResult.registry.fileSize,
        formattedFileSize: uploadResult.registry.getFormattedFileSize?.() || 'Unknown',
        storageProvider: uploadResult.registry.storageProvider || 'local',
        fileUrl: fileUrl || '',
        isImage: uploadResult.registry.isImage?.() || true,
        createdAt: uploadResult.registry.createdAt,
        updatedAt: uploadResult.registry.updatedAt,
      };

      return ApiResponse.success(responseDto, 'Diary cover photo uploaded successfully', 201);
    } catch (error) {
      throw new BadRequestException(`Failed to upload diary cover photo: ${error.message}`);
    }
  }

  @Get('cover-photo')
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @ApiOperation({
    summary: 'Get diary cover photo',
    description: 'Get the current diary cover photo for the authenticated student.',
  })
  @ApiOkResponseWithType(DiaryCoverPhotoResponseDto, 'Diary cover photo retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Diary cover photo not found')
  async getDiaryCoverPhoto(@Req() req: any): Promise<ApiResponse<DiaryCoverPhotoResponseDto | null>> {
    const userId = req.user.id;

    try {
      // Get the user's diary
      const diary = await this.diaryService.getDiary(userId);
      if (!diary) {
        return ApiResponse.success(null, 'No diary found for user');
      }

      // Get the cover photo registry
      const coverRegistry = await this.fileRegistryService.getFile(FileEntityType.DIARY_COVER, diary.id);

      if (!coverRegistry) {
        return ApiResponse.success(null, 'No cover photo found for diary');
      }

      // Get the file URL
      const fileUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_COVER, diary.id);

      // Create response DTO
      const responseDto: DiaryCoverPhotoResponseDto = {
        id: coverRegistry.id,
        diaryId: diary.id,
        userId: userId,
        fileName: coverRegistry.fileName,
        mimeType: coverRegistry.mimeType,
        fileSize: coverRegistry.fileSize,
        formattedFileSize: coverRegistry.getFormattedFileSize?.() || 'Unknown',
        storageProvider: coverRegistry.storageProvider || 'local',
        fileUrl: fileUrl || '',
        isImage: coverRegistry.isImage?.() || true,
        createdAt: coverRegistry.createdAt,
        updatedAt: coverRegistry.updatedAt,
      };

      return ApiResponse.success(responseDto, 'Diary cover photo retrieved successfully');
    } catch (error) {
      throw new NotFoundException(`Failed to retrieve diary cover photo: ${error.message}`);
    }
  }

  @Delete('cover-photo')
  @UseGuards(StudentGuard, SubscriptionFeatureGuard)
  @RequireFeature(FeatureType.HEC_USER_DIARY)
  @ApiOperation({
    summary: 'Delete diary cover photo',
    description: 'Delete the current diary cover photo for the authenticated student.',
  })
  @ApiOkResponseWithType(Object, 'Diary cover photo deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Diary cover photo not found')
  async deleteDiaryCoverPhoto(@Req() req: any): Promise<ApiResponse<null>> {
    const userId = req.user.id;

    try {
      // Get the user's diary
      const diary = await this.diaryService.getDiary(userId);
      if (!diary) {
        throw new NotFoundException('No diary found for user');
      }

      // Get the cover photo registry
      const coverRegistry = await this.fileRegistryService.getFile(FileEntityType.DIARY_COVER, diary.id);

      if (!coverRegistry) {
        throw new NotFoundException('No cover photo found for diary');
      }

      // Delete the file from disk
      if (coverRegistry.filePath) {
        this.fileRegistryService.deleteFile(coverRegistry.filePath);
      }

      // Delete the registry entry
      await this.fileRegistryService.deleteDiaryCoverRegistry(coverRegistry.id);

      return ApiResponse.success(null, 'Diary cover photo deleted successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete diary cover photo: ${error.message}`);
    }
  }
}
